import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { config } from '@/config/config';
import { unauthorized, forbidden } from '@/middleware/errorHandler';
import { DatabaseService } from '@/services/database';

export interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    email: string;
    username: string;
    role: string;
    isActive: boolean;
    isBanned: boolean;
  };
}

export const authMiddleware = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw unauthorized('Access token is required');
    }

    const token = authHeader.substring(7);
    
    if (!token) {
      throw unauthorized('Access token is required');
    }

    // Verify JWT token
    const decoded = jwt.verify(token, config.jwtSecret) as any;
    
    if (!decoded || !decoded.userId) {
      throw unauthorized('Invalid token');
    }

    // Get user from database
    const user = await DatabaseService.user.findUnique({
      where: { id: decoded.userId },
      select: {
        id: true,
        email: true,
        username: true,
        role: true,
        isActive: true,
        isBanned: true,
        banExpires: true,
      },
    });

    if (!user) {
      throw unauthorized('User not found');
    }

    if (!user.isActive) {
      throw unauthorized('Account is deactivated');
    }

    if (user.isBanned) {
      const banExpired = user.banExpires && new Date() > user.banExpires;
      if (!banExpired) {
        throw forbidden('Account is banned');
      }
      
      // Unban user if ban has expired
      await DatabaseService.user.update({
        where: { id: user.id },
        data: {
          isBanned: false,
          banReason: null,
          banExpires: null,
        },
      });
      user.isBanned = false;
    }

    // Attach user to request
    req.user = user;
    next();
  } catch (error) {
    next(error);
  }
};

export const requireRole = (roles: string[]) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    if (!req.user) {
      throw unauthorized('Authentication required');
    }

    if (!roles.includes(req.user.role)) {
      throw forbidden('Insufficient permissions');
    }

    next();
  };
};

export const requireAdmin = requireRole(['ADMIN', 'SUPER_ADMIN']);
export const requireModerator = requireRole(['MODERATOR', 'ADMIN', 'SUPER_ADMIN']);

export const optionalAuth = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return next();
    }

    const token = authHeader.substring(7);
    
    if (!token) {
      return next();
    }

    // Verify JWT token
    const decoded = jwt.verify(token, config.jwtSecret) as any;
    
    if (!decoded || !decoded.userId) {
      return next();
    }

    // Get user from database
    const user = await DatabaseService.user.findUnique({
      where: { id: decoded.userId },
      select: {
        id: true,
        email: true,
        username: true,
        role: true,
        isActive: true,
        isBanned: true,
      },
    });

    if (user && user.isActive && !user.isBanned) {
      req.user = user;
    }

    next();
  } catch (error) {
    // Ignore auth errors for optional auth
    next();
  }
};
