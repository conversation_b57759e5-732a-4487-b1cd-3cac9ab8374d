# CS Arena - Installation Guide

This guide will help you set up the CS 1.6 Arena platform for development or production.

## 🎯 Quick Start

### Prerequisites

- **Node.js 18+** - [Download here](https://nodejs.org/)
- **Docker & Docker Compose** - [Download here](https://docker.com/)
- **Git** - [Download here](https://git-scm.com/)

### Automated Setup

#### Windows
```bash
# Run the setup script
setup.bat
```

#### Linux/macOS
```bash
# Make script executable and run
chmod +x setup.sh
./setup.sh
```

### Manual Setup

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-org/cs-arena.git
   cd cs-arena
   ```

2. **Install dependencies**
   ```bash
   npm run setup
   ```

3. **Configure environment**
   ```bash
   # Copy environment files
   cp backend/.env.example backend/.env
   cp frontend/.env.example frontend/.env
   
   # Edit the files with your configuration
   ```

4. **Start services**
   ```bash
   # Start databases and services
   docker-compose up -d
   
   # Initialize database
   cd backend
   npx prisma migrate dev
   npx prisma db seed
   cd ..
   
   # Start development servers
   npm run dev
   ```

## 🔧 Configuration

### Backend Configuration (backend/.env)

```env
# Database
DATABASE_URL=postgresql://cs_arena_user:cs_arena_password@localhost:5432/cs_arena

# Redis
REDIS_URL=redis://localhost:6379

# RabbitMQ
RABBITMQ_URL=amqp://cs_arena_user:cs_arena_password@localhost:5672

# JWT
JWT_SECRET=your-super-secret-jwt-key-change-in-production

# OAuth (Optional)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
STEAM_API_KEY=your-steam-api-key

# Game Servers
CS16_SERVER_IMAGE=cajuclc/cstrike-docker
CS16_SERVER_PORT_RANGE_START=27015
CS16_SERVER_PORT_RANGE_END=27115
```

### Frontend Configuration (frontend/.env)

```env
# API Configuration
REACT_APP_API_URL=http://localhost:5000/api
REACT_APP_WS_URL=ws://localhost:5000

# OAuth
REACT_APP_GOOGLE_CLIENT_ID=your-google-client-id
REACT_APP_STEAM_LOGIN_URL=http://localhost:5000/api/auth/steam

# Feature Flags
REACT_APP_ENABLE_TOURNAMENTS=true
REACT_APP_ENABLE_TEAMS=true
REACT_APP_ENABLE_CHAT=true
```

## 🚀 Running the Application

### Development Mode

```bash
# Start all services
npm run dev

# Or start individually
npm run dev:backend   # Backend only
npm run dev:frontend  # Frontend only
```

### Production Mode

```bash
# Build applications
npm run build

# Start production server
npm start
```

### Using Docker

```bash
# Development
docker-compose up -d

# Production
docker-compose -f docker-compose.prod.yml up -d
```

## 🗄️ Database Management

### Migrations

```bash
cd backend

# Create new migration
npx prisma migrate dev --name migration_name

# Apply migrations
npx prisma migrate deploy

# Reset database (development only)
npx prisma migrate reset
```

### Seeding

```bash
cd backend

# Seed database with sample data
npx prisma db seed
```

### Database Admin

```bash
cd backend

# Open Prisma Studio
npx prisma studio
```

## 🎮 Game Server Setup

### Docker Configuration

The platform uses Docker containers to run CS 1.6 servers. The configuration is in `game-servers/docker/`.

### Server Provisioning

Servers are automatically provisioned when matches are found. You can also manually provision servers via the admin panel.

### Port Configuration

Configure the port range for game servers in the backend `.env` file:

```env
CS16_SERVER_PORT_RANGE_START=27015
CS16_SERVER_PORT_RANGE_END=27115
```

## 🔍 Monitoring & Logs

### Application Logs

```bash
# Backend logs
tail -f backend/logs/app.log

# Docker logs
docker-compose logs -f backend
docker-compose logs -f frontend
```

### Health Checks

- Backend API: `http://localhost:5000/health`
- Database: Prisma Studio
- Redis: Redis CLI
- RabbitMQ: Management UI at `http://localhost:15672`

## 🧪 Testing

### Backend Tests

```bash
cd backend
npm test
npm run test:coverage
```

### Frontend Tests

```bash
cd frontend
npm test
npm run test:coverage
```

### End-to-End Tests

```bash
# Run E2E tests
npm run test:e2e
```

## 📦 Deployment

### Environment Setup

1. **Production Environment Variables**
   - Set secure JWT secrets
   - Configure production database
   - Set up monitoring services

2. **Infrastructure**
   - PostgreSQL database
   - Redis cache
   - RabbitMQ message queue
   - Docker/Kubernetes for game servers

### Docker Deployment

```bash
# Build production images
docker-compose -f docker-compose.prod.yml build

# Deploy
docker-compose -f docker-compose.prod.yml up -d
```

### Kubernetes Deployment

```bash
# Apply Kubernetes manifests
kubectl apply -f infrastructure/kubernetes/
```

## 🔧 Troubleshooting

### Common Issues

1. **Port conflicts**
   - Check if ports 3000, 5000, 5432, 6379, 5672 are available
   - Modify docker-compose.yml if needed

2. **Database connection issues**
   - Ensure PostgreSQL is running
   - Check DATABASE_URL in .env
   - Verify database credentials

3. **Game server provisioning fails**
   - Check Docker daemon is running
   - Verify CS16_SERVER_IMAGE is available
   - Check port range availability

4. **Frontend build issues**
   - Clear node_modules and reinstall
   - Check Node.js version (18+ required)
   - Verify environment variables

### Getting Help

- Check the logs for error messages
- Review the configuration files
- Ensure all prerequisites are installed
- Check the GitHub issues for known problems

## 📚 Additional Resources

- [Backend API Documentation](backend/README.md)
- [Frontend Development Guide](frontend/README.md)
- [Game Server Configuration](game-servers/README.md)
- [Deployment Guide](docs/DEPLOYMENT.md)

## 🎮 Default Accounts

After seeding the database, you can use these accounts:

- **Admin**: <EMAIL> / admin123456
- **Player 1**: <EMAIL> / password123
- **Player 2**: <EMAIL> / password123
- **Player 3**: <EMAIL> / password123

## 🔐 Security Notes

- Change default passwords in production
- Use strong JWT secrets
- Enable HTTPS in production
- Configure proper CORS settings
- Set up rate limiting
- Enable database SSL in production

---

**Happy gaming! 🎮**
