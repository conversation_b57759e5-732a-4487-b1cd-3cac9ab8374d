import { Router } from 'express';
import { body, param } from 'express-validator';
import { ServerController } from '@/controllers/serverController';
import { asyncHandler } from '@/middleware/errorHandler';
import { requireAdmin } from '@/middleware/auth';

const router = Router();

// Validation rules
const provisionValidation = [
  body('matchId').isUUID(),
  body('map').isIn(['de_dust2', 'de_inferno', 'de_nuke', 'de_train', 'de_cache', 'de_mirage', 'de_cbble', 'de_overpass']),
  body('maxPlayers').isInt({ min: 2, max: 32 }),
  body('config').optional().isObject(),
];

const serverIdValidation = [
  param('serverId').isString(),
];

// Routes
router.post('/provision', requireAdmin, provisionValidation, asyncHandler(ServerController.provisionServer));
router.delete('/:serverId', requireAdmin, serverIdValidation, asyncHandler(ServerController.terminateServer));
router.get('/:serverId/status', serverIdValidation, asyncHandler(ServerController.getServerStatus));
router.get('/:serverId/logs', requireAdmin, serverIdValidation, asyncHandler(ServerController.getServerLogs));

// Server management
router.get('/list', requireAdmin, asyncHandler(ServerController.listServers));
router.get('/stats', requireAdmin, asyncHandler(ServerController.getServerStats));
router.post('/:serverId/restart', requireAdmin, serverIdValidation, asyncHandler(ServerController.restartServer));
router.post('/:serverId/command', requireAdmin, serverIdValidation, asyncHandler(ServerController.executeCommand));

// Health checks
router.get('/health', asyncHandler(ServerController.healthCheck));

export default router;
