import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Create admin user
  const adminPassword = await bcrypt.hash('admin123456', 12);
  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      username: 'admin',
      displayName: 'CS Arena Admin',
      passwordHash: adminPassword,
      emailVerified: true,
      role: 'SUPER_ADMIN',
      profile: {
        create: {
          firstName: 'CS Arena',
          lastName: 'Admin',
          country: 'BR',
          language: 'en',
        },
      },
      stats: {
        create: {
          eloRating: 2000,
          rank: 'ELITE',
        },
      },
    },
  });

  console.log('✅ Admin user created:', admin.username);

  // Create test users
  const testUsers = [
    {
      email: '<EMAIL>',
      username: 'player1',
      displayName: 'Test Player 1',
      eloRating: 1200,
      rank: 'SILVER' as const,
    },
    {
      email: '<EMAIL>',
      username: 'player2',
      displayName: 'Test Player 2',
      eloRating: 1500,
      rank: 'GOLD' as const,
    },
    {
      email: '<EMAIL>',
      username: 'player3',
      displayName: 'Test Player 3',
      eloRating: 1800,
      rank: 'DIAMOND' as const,
    },
  ];

  for (const userData of testUsers) {
    const password = await bcrypt.hash('password123', 12);
    const user = await prisma.user.upsert({
      where: { email: userData.email },
      update: {},
      create: {
        email: userData.email,
        username: userData.username,
        displayName: userData.displayName,
        passwordHash: password,
        emailVerified: true,
        profile: {
          create: {
            country: 'BR',
            language: 'en',
          },
        },
        stats: {
          create: {
            eloRating: userData.eloRating,
            rank: userData.rank,
            totalMatches: Math.floor(Math.random() * 100) + 10,
            wins: Math.floor(Math.random() * 50) + 5,
            losses: Math.floor(Math.random() * 40) + 3,
            kills: Math.floor(Math.random() * 1000) + 100,
            deaths: Math.floor(Math.random() * 800) + 80,
            assists: Math.floor(Math.random() * 300) + 30,
            headshots: Math.floor(Math.random() * 200) + 20,
          },
        },
      },
    });
    console.log('✅ Test user created:', user.username);
  }

  // Create sample team
  const team = await prisma.team.create({
    data: {
      name: 'CS Arena Legends',
      tag: 'CAL',
      description: 'The legendary team of CS Arena',
      ownerId: admin.id,
      members: {
        create: [
          {
            userId: admin.id,
            role: 'OWNER',
          },
        ],
      },
    },
  });

  console.log('✅ Sample team created:', team.name);

  // Create sample tournament
  const tournament = await prisma.tournament.create({
    data: {
      name: 'CS Arena Championship 2025',
      description: 'The biggest CS 1.6 tournament of the year',
      type: 'SINGLE_ELIMINATION',
      status: 'UPCOMING',
      maxParticipants: 64,
      prizePool: 10000.00,
      startDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
      registrationEnd: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000), // 5 days from now
      rules: {
        mapPool: ['de_dust2', 'de_inferno', 'de_nuke', 'de_train', 'de_cache'],
        matchFormat: 'BO3',
        overtime: true,
      },
    },
  });

  console.log('✅ Sample tournament created:', tournament.name);

  // Create sample match
  const match = await prisma.match.create({
    data: {
      map: 'de_dust2',
      gameMode: 'COMPETITIVE',
      status: 'FINISHED',
      maxRounds: 30,
      currentRound: 16,
      scoreTeamA: 16,
      scoreTeamB: 8,
      winnerTeam: 'A',
      startedAt: new Date(Date.now() - 60 * 60 * 1000), // 1 hour ago
      endedAt: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
      players: {
        create: [
          {
            userId: testUsers[0].email === '<EMAIL>' ? 
              (await prisma.user.findUnique({ where: { email: testUsers[0].email } }))!.id : admin.id,
            team: 'A',
            kills: 24,
            deaths: 12,
            assists: 8,
            headshots: 15,
            score: 2400,
            mvp: true,
          },
          {
            userId: testUsers[1].email === '<EMAIL>' ? 
              (await prisma.user.findUnique({ where: { email: testUsers[1].email } }))!.id : admin.id,
            team: 'B',
            kills: 18,
            deaths: 16,
            assists: 6,
            headshots: 10,
            score: 1800,
            mvp: false,
          },
        ],
      },
    },
  });

  console.log('✅ Sample match created with ID:', match.id);

  // Create sample notifications
  const users = await prisma.user.findMany();
  for (const user of users) {
    await prisma.notification.create({
      data: {
        userId: user.id,
        type: 'SYSTEM_ANNOUNCEMENT',
        title: 'Welcome to CS Arena!',
        message: 'Welcome to the ultimate CS 1.6 competitive platform. Start your journey by joining the matchmaking queue!',
        data: {
          action: 'navigate',
          url: '/matchmaking',
        },
      },
    });
  }

  console.log('✅ Sample notifications created');

  // Create sample chat messages
  await prisma.chatMessage.createMany({
    data: [
      {
        senderId: admin.id,
        type: 'GLOBAL',
        channel: 'global',
        content: 'Welcome everyone to CS Arena! Let\'s have some great matches!',
      },
      {
        senderId: admin.id,
        type: 'GLOBAL',
        channel: 'global',
        content: 'Remember to follow the rules and have fun!',
      },
    ],
  });

  console.log('✅ Sample chat messages created');

  console.log('🎉 Database seeding completed successfully!');
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
