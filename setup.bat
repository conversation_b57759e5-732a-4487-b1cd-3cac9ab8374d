@echo off
setlocal enabledelayedexpansion

REM CS Arena Setup Script for Windows
REM This script sets up the development environment for CS Arena

echo.
echo CS Arena Setup Script
echo ========================
echo.

REM Check prerequisites
echo [INFO] Checking prerequisites...

REM Check Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js is not installed. Please install Node.js 18+ from https://nodejs.org/
    pause
    exit /b 1
)

for /f "tokens=1 delims=." %%a in ('node --version') do set NODE_MAJOR=%%a
set NODE_MAJOR=%NODE_MAJOR:v=%
if %NODE_MAJOR% LSS 18 (
    echo [ERROR] Node.js version 18+ is required. Current version: 
    node --version
    pause
    exit /b 1
)

echo [SUCCESS] Node.js is installed
node --version

REM Check npm
npm --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] npm is not installed
    pause
    exit /b 1
)

echo [SUCCESS] npm is installed
npm --version

REM Check Docker
docker --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker is not installed. Please install Docker from https://docker.com/
    pause
    exit /b 1
)

echo [SUCCESS] Docker is installed
docker --version

REM Check Docker Compose
docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker Compose is not installed
    pause
    exit /b 1
)

echo [SUCCESS] Docker Compose is installed
docker-compose --version

REM Install dependencies
echo.
echo [INFO] Installing dependencies...

REM Root dependencies
echo [INFO] Installing root dependencies...
call npm install
if errorlevel 1 (
    echo [ERROR] Failed to install root dependencies
    pause
    exit /b 1
)

REM Backend dependencies
echo [INFO] Installing backend dependencies...
cd backend
call npm install
if errorlevel 1 (
    echo [ERROR] Failed to install backend dependencies
    pause
    exit /b 1
)
cd ..

REM Frontend dependencies
echo [INFO] Installing frontend dependencies...
cd frontend
call npm install
if errorlevel 1 (
    echo [ERROR] Failed to install frontend dependencies
    pause
    exit /b 1
)
cd ..

echo [SUCCESS] All dependencies installed

REM Setup environment files
echo.
echo [INFO] Setting up environment files...

REM Backend environment
if not exist "backend\.env" (
    echo [INFO] Creating backend .env file...
    copy "backend\.env.example" "backend\.env" >nul
    echo [WARNING] Please edit backend\.env with your configuration
) else (
    echo [WARNING] backend\.env already exists
)

REM Frontend environment
if not exist "frontend\.env" (
    echo [INFO] Creating frontend .env file...
    copy "frontend\.env.example" "frontend\.env" >nul
    echo [WARNING] Please edit frontend\.env with your configuration
) else (
    echo [WARNING] frontend\.env already exists
)

REM Generate Prisma client
echo.
echo [INFO] Generating Prisma client...
cd backend
call npx prisma generate
if errorlevel 1 (
    echo [ERROR] Failed to generate Prisma client
    pause
    exit /b 1
)
cd ..

echo [SUCCESS] Prisma client generated

REM Build Docker images
echo.
echo [INFO] Building Docker images...
call docker-compose build
if errorlevel 1 (
    echo [ERROR] Failed to build Docker images
    pause
    exit /b 1
)

echo [SUCCESS] Docker images built

REM Create logs directory
echo.
echo [INFO] Creating logs directories...
if not exist "backend\logs" mkdir "backend\logs"
if not exist "game-servers\logs" mkdir "game-servers\logs"

echo [SUCCESS] Logs directories created

REM Setup complete
echo.
echo [SUCCESS] Setup completed successfully!
echo.
echo Next Steps:
echo ==============
echo 1. Edit the .env files in backend\ and frontend\ directories
echo 2. Start the development environment:
echo    docker-compose up -d  # Start databases
echo    npm run dev           # Start development servers
echo.
echo 3. Initialize the database:
echo    cd backend
echo    npx prisma migrate dev
echo    npx prisma db seed
echo.
echo 4. Access the application:
echo    Frontend: http://localhost:3000
echo    Backend API: http://localhost:5000
echo    Database Admin: npx prisma studio
echo.
echo 📚 Documentation:
echo    README.md - Project overview and setup
echo    backend\README.md - Backend API documentation
echo    frontend\README.md - Frontend development guide
echo.
echo 🎮 Happy coding! Welcome to CS Arena!
echo.
pause
