#!/bin/bash

# CS Arena Setup Script
# This script sets up the development environment for CS Arena

set -e

echo "🎮 CS Arena Setup Script"
echo "========================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running on Windows (Git Bash/WSL)
if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
    print_warning "Detected Windows environment. Some commands may need adjustment."
fi

# Check prerequisites
print_status "Checking prerequisites..."

# Check Node.js
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js 18+ from https://nodejs.org/"
    exit 1
fi

NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    print_error "Node.js version 18+ is required. Current version: $(node --version)"
    exit 1
fi

print_success "Node.js $(node --version) is installed"

# Check npm
if ! command -v npm &> /dev/null; then
    print_error "npm is not installed"
    exit 1
fi

print_success "npm $(npm --version) is installed"

# Check Docker
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker from https://docker.com/"
    exit 1
fi

print_success "Docker $(docker --version) is installed"

# Check Docker Compose
if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose is not installed"
    exit 1
fi

print_success "Docker Compose $(docker-compose --version) is installed"

# Install dependencies
print_status "Installing dependencies..."

# Root dependencies
print_status "Installing root dependencies..."
npm install

# Backend dependencies
print_status "Installing backend dependencies..."
cd backend
npm install
cd ..

# Frontend dependencies
print_status "Installing frontend dependencies..."
cd frontend
npm install
cd ..

print_success "All dependencies installed"

# Setup environment files
print_status "Setting up environment files..."

# Backend environment
if [ ! -f "backend/.env" ]; then
    print_status "Creating backend .env file..."
    cp backend/.env.example backend/.env
    print_warning "Please edit backend/.env with your configuration"
else
    print_warning "backend/.env already exists"
fi

# Frontend environment
if [ ! -f "frontend/.env" ]; then
    print_status "Creating frontend .env file..."
    cp frontend/.env.example frontend/.env
    print_warning "Please edit frontend/.env with your configuration"
else
    print_warning "frontend/.env already exists"
fi

# Generate Prisma client
print_status "Generating Prisma client..."
cd backend
npx prisma generate
cd ..

print_success "Prisma client generated"

# Build Docker images
print_status "Building Docker images..."
docker-compose build

print_success "Docker images built"

# Create logs directory
print_status "Creating logs directory..."
mkdir -p backend/logs
mkdir -p game-servers/logs

print_success "Logs directories created"

# Setup complete
print_success "Setup completed successfully!"

echo ""
echo "🚀 Next Steps:"
echo "=============="
echo "1. Edit the .env files in backend/ and frontend/ directories"
echo "2. Start the development environment:"
echo "   ${BLUE}docker-compose up -d${NC}  # Start databases"
echo "   ${BLUE}npm run dev${NC}           # Start development servers"
echo ""
echo "3. Initialize the database:"
echo "   ${BLUE}cd backend${NC}"
echo "   ${BLUE}npx prisma migrate dev${NC}"
echo "   ${BLUE}npx prisma db seed${NC}"
echo ""
echo "4. Access the application:"
echo "   Frontend: ${BLUE}http://localhost:3000${NC}"
echo "   Backend API: ${BLUE}http://localhost:5000${NC}"
echo "   Database Admin: ${BLUE}npx prisma studio${NC}"
echo ""
echo "📚 Documentation:"
echo "   README.md - Project overview and setup"
echo "   backend/README.md - Backend API documentation"
echo "   frontend/README.md - Frontend development guide"
echo ""
echo "🎮 Happy coding! Welcome to CS Arena!"
