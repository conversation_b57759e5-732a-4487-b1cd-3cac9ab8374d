import { Router } from 'express';
import { body, param, query } from 'express-validator';
import { TournamentController } from '@/controllers/tournamentController';
import { asyncHandler } from '@/middleware/errorHandler';
import { requireModerator } from '@/middleware/auth';

const router = Router();

// Validation rules
const createTournamentValidation = [
  body('name').isLength({ min: 3, max: 100 }),
  body('description').optional().isLength({ max: 1000 }),
  body('type').isIn(['SINGLE_ELIMINATION', 'DOUBLE_ELIMINATION', 'ROUND_ROBIN', 'SWISS']),
  body('maxParticipants').isInt({ min: 4, max: 256 }),
  body('entryFee').optional().isDecimal({ decimal_digits: '0,2' }),
  body('prizePool').optional().isDecimal({ decimal_digits: '0,2' }),
  body('startDate').isISO8601(),
  body('registrationEnd').isISO8601(),
  body('rules').optional().isObject(),
];

const tournamentIdValidation = [
  param('tournamentId').isUUID(),
];

const updateTournamentValidation = [
  body('name').optional().isLength({ min: 3, max: 100 }),
  body('description').optional().isLength({ max: 1000 }),
  body('maxParticipants').optional().isInt({ min: 4, max: 256 }),
  body('entryFee').optional().isDecimal({ decimal_digits: '0,2' }),
  body('prizePool').optional().isDecimal({ decimal_digits: '0,2' }),
  body('startDate').optional().isISO8601(),
  body('registrationEnd').optional().isISO8601(),
  body('rules').optional().isObject(),
];

const searchValidation = [
  query('q').optional().isLength({ min: 2, max: 50 }),
  query('status').optional().isIn(['UPCOMING', 'REGISTRATION_OPEN', 'REGISTRATION_CLOSED', 'LIVE', 'FINISHED', 'CANCELLED']),
  query('type').optional().isIn(['SINGLE_ELIMINATION', 'DOUBLE_ELIMINATION', 'ROUND_ROBIN', 'SWISS']),
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 50 }),
];

// Routes
router.get('/', searchValidation, asyncHandler(TournamentController.getTournaments));
router.post('/', requireModerator, createTournamentValidation, asyncHandler(TournamentController.createTournament));

router.get('/featured', asyncHandler(TournamentController.getFeaturedTournaments));
router.get('/upcoming', asyncHandler(TournamentController.getUpcomingTournaments));
router.get('/live', asyncHandler(TournamentController.getLiveTournaments));

router.get('/:tournamentId', tournamentIdValidation, asyncHandler(TournamentController.getTournament));
router.put('/:tournamentId', requireModerator, tournamentIdValidation, updateTournamentValidation, asyncHandler(TournamentController.updateTournament));
router.delete('/:tournamentId', requireModerator, tournamentIdValidation, asyncHandler(TournamentController.deleteTournament));

// Tournament participation
router.post('/:tournamentId/register', tournamentIdValidation, asyncHandler(TournamentController.registerForTournament));
router.delete('/:tournamentId/register', tournamentIdValidation, asyncHandler(TournamentController.unregisterFromTournament));
router.get('/:tournamentId/participants', tournamentIdValidation, asyncHandler(TournamentController.getTournamentParticipants));

// Tournament teams
router.post('/:tournamentId/teams/:teamId', tournamentIdValidation, asyncHandler(TournamentController.registerTeam));
router.delete('/:tournamentId/teams/:teamId', tournamentIdValidation, asyncHandler(TournamentController.unregisterTeam));
router.get('/:tournamentId/teams', tournamentIdValidation, asyncHandler(TournamentController.getTournamentTeams));

// Tournament brackets
router.get('/:tournamentId/brackets', tournamentIdValidation, asyncHandler(TournamentController.getTournamentBrackets));
router.post('/:tournamentId/brackets/generate', requireModerator, tournamentIdValidation, asyncHandler(TournamentController.generateBrackets));
router.put('/:tournamentId/brackets/:bracketId', requireModerator, tournamentIdValidation, asyncHandler(TournamentController.updateBracket));

// Tournament management
router.post('/:tournamentId/start', requireModerator, tournamentIdValidation, asyncHandler(TournamentController.startTournament));
router.post('/:tournamentId/finish', requireModerator, tournamentIdValidation, asyncHandler(TournamentController.finishTournament));
router.post('/:tournamentId/cancel', requireModerator, tournamentIdValidation, asyncHandler(TournamentController.cancelTournament));

export default router;
