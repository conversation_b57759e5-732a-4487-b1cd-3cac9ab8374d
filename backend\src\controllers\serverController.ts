import { Request, Response } from 'express';
import { validationResult } from 'express-validator';
import { ServerProvisioningService } from '@/services/serverProvisioning';
import { badRequest, notFound, internalServerError } from '@/middleware/errorHandler';
import { AuthenticatedRequest } from '@/middleware/auth';
import { logger } from '@/utils/logger';

export class ServerController {
  static async provisionServer(req: AuthenticatedRequest, res: Response): Promise<void> {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw badRequest('Validation failed');
    }

    const { matchId, map, maxPlayers, config } = req.body;

    try {
      const serverConfig = {
        matchId,
        map,
        maxPlayers: maxPlayers || 10,
        serverName: `CS Arena Match ${matchId}`,
        rconPassword: `rcon_${matchId}`,
        serverPassword: config?.password || '',
        tickrate: config?.tickrate || 100,
        players: config?.players || [],
      };

      const serverInfo = await ServerProvisioningService.provisionServer(serverConfig);

      logger.info(`Server provisioned by admin ${req.user?.username}: ${serverInfo.id}`);

      res.status(201).json({
        success: true,
        data: {
          serverId: serverInfo.id,
          ip: serverInfo.ip,
          port: serverInfo.port,
          status: serverInfo.status,
          matchId: serverInfo.matchId,
          connectString: `connect ${serverInfo.ip}:${serverInfo.port}${serverInfo.config.serverPassword ? `;password ${serverInfo.config.serverPassword}` : ''}`,
        },
      });
    } catch (error) {
      logger.error('Failed to provision server:', error);
      throw internalServerError('Failed to provision server');
    }
  }

  static async terminateServer(req: AuthenticatedRequest, res: Response): Promise<void> {
    const { serverId } = req.params;

    try {
      await ServerProvisioningService.terminateServer(serverId);

      logger.info(`Server terminated by admin ${req.user?.username}: ${serverId}`);

      res.json({
        success: true,
        message: 'Server terminated successfully',
      });
    } catch (error) {
      logger.error(`Failed to terminate server ${serverId}:`, error);
      
      if (error instanceof Error && error.message.includes('not found')) {
        throw notFound('Server not found');
      }
      
      throw internalServerError('Failed to terminate server');
    }
  }

  static async getServerStatus(req: Request, res: Response): Promise<void> {
    const { serverId } = req.params;

    try {
      const serverInfo = await ServerProvisioningService.getServerInfo(serverId);

      if (!serverInfo) {
        throw notFound('Server not found');
      }

      res.json({
        success: true,
        data: {
          id: serverInfo.id,
          status: serverInfo.status,
          ip: serverInfo.ip,
          port: serverInfo.port,
          matchId: serverInfo.matchId,
          createdAt: serverInfo.createdAt,
          config: {
            map: serverInfo.config.map,
            maxPlayers: serverInfo.config.maxPlayers,
            serverName: serverInfo.config.serverName,
          },
          connectString: `connect ${serverInfo.ip}:${serverInfo.port}${serverInfo.config.serverPassword ? `;password ${serverInfo.config.serverPassword}` : ''}`,
        },
      });
    } catch (error) {
      logger.error(`Failed to get server status ${serverId}:`, error);
      throw internalServerError('Failed to get server status');
    }
  }

  static async getServerLogs(req: AuthenticatedRequest, res: Response): Promise<void> {
    const { serverId } = req.params;
    const { lines = 100 } = req.query;

    try {
      const serverInfo = await ServerProvisioningService.getServerInfo(serverId);

      if (!serverInfo) {
        throw notFound('Server not found');
      }

      // TODO: Implement log retrieval from Docker container
      // This would involve getting logs from the container
      const logs = 'Log retrieval not implemented yet';

      res.json({
        success: true,
        data: {
          serverId,
          logs,
          lines: parseInt(lines as string),
        },
      });
    } catch (error) {
      logger.error(`Failed to get server logs ${serverId}:`, error);
      throw internalServerError('Failed to get server logs');
    }
  }

  static async listServers(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const servers = await ServerProvisioningService.listServers();

      res.json({
        success: true,
        data: {
          servers: servers.map(server => ({
            id: server.id,
            status: server.status,
            ip: server.ip,
            port: server.port,
            matchId: server.matchId,
            createdAt: server.createdAt,
            config: {
              map: server.config.map,
              maxPlayers: server.config.maxPlayers,
              serverName: server.config.serverName,
            },
          })),
          total: servers.length,
        },
      });
    } catch (error) {
      logger.error('Failed to list servers:', error);
      throw internalServerError('Failed to list servers');
    }
  }

  static async getServerStats(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const stats = await ServerProvisioningService.getServerStats();

      res.json({
        success: true,
        data: stats,
      });
    } catch (error) {
      logger.error('Failed to get server stats:', error);
      throw internalServerError('Failed to get server stats');
    }
  }

  static async restartServer(req: AuthenticatedRequest, res: Response): Promise<void> {
    const { serverId } = req.params;

    try {
      const serverInfo = await ServerProvisioningService.getServerInfo(serverId);

      if (!serverInfo) {
        throw notFound('Server not found');
      }

      // Terminate and recreate server
      await ServerProvisioningService.terminateServer(serverId);
      const newServerInfo = await ServerProvisioningService.provisionServer(serverInfo.config);

      logger.info(`Server restarted by admin ${req.user?.username}: ${serverId} -> ${newServerInfo.id}`);

      res.json({
        success: true,
        data: {
          oldServerId: serverId,
          newServerId: newServerInfo.id,
          ip: newServerInfo.ip,
          port: newServerInfo.port,
          status: newServerInfo.status,
        },
      });
    } catch (error) {
      logger.error(`Failed to restart server ${serverId}:`, error);
      throw internalServerError('Failed to restart server');
    }
  }

  static async executeCommand(req: AuthenticatedRequest, res: Response): Promise<void> {
    const { serverId } = req.params;
    const { command } = req.body;

    if (!command) {
      throw badRequest('Command is required');
    }

    try {
      const output = await ServerProvisioningService.executeCommand(serverId, command);

      logger.info(`Command executed by admin ${req.user?.username} on server ${serverId}: ${command}`);

      res.json({
        success: true,
        data: {
          serverId,
          command,
          output,
        },
      });
    } catch (error) {
      logger.error(`Failed to execute command on server ${serverId}:`, error);
      
      if (error instanceof Error && error.message.includes('not found')) {
        throw notFound('Server not found');
      }
      
      throw internalServerError('Failed to execute command');
    }
  }

  static async healthCheck(req: Request, res: Response): Promise<void> {
    try {
      const isHealthy = await ServerProvisioningService.healthCheck();

      res.json({
        success: true,
        data: {
          status: isHealthy ? 'healthy' : 'unhealthy',
          timestamp: new Date().toISOString(),
        },
      });
    } catch (error) {
      logger.error('Server health check failed:', error);
      throw internalServerError('Health check failed');
    }
  }
}
