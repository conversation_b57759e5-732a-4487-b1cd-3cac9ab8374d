import Docker from 'dockerode';
import { v4 as uuidv4 } from 'uuid';
import { config } from '@/config/config';
import { logger } from '@/utils/logger';
import { RedisService } from '@/services/redis';
import { RabbitMQService } from '@/services/rabbitmq';

interface ServerConfig {
  matchId: string;
  map: string;
  maxPlayers: number;
  serverName: string;
  rconPassword: string;
  serverPassword?: string;
  tickrate?: number;
  players: string[];
}

interface ServerInfo {
  id: string;
  containerId: string;
  matchId: string;
  ip: string;
  port: number;
  status: 'starting' | 'running' | 'stopping' | 'stopped' | 'error';
  createdAt: Date;
  config: ServerConfig;
}

class ServerProvisioningServiceClass {
  private static instance: ServerProvisioningServiceClass;
  private docker: Docker;
  private servers: Map<string, ServerInfo> = new Map();
  private portRange = {
    start: config.cs16ServerPortRangeStart,
    end: config.cs16ServerPortRangeEnd,
  };
  private usedPorts: Set<number> = new Set();

  private constructor() {
    this.docker = new Docker();
    this.initializeService();
  }

  public static getInstance(): ServerProvisioningServiceClass {
    if (!ServerProvisioningServiceClass.instance) {
      ServerProvisioningServiceClass.instance = new ServerProvisioningServiceClass();
    }
    return ServerProvisioningServiceClass.instance;
  }

  private async initializeService(): Promise<void> {
    try {
      // Load existing servers from Redis
      await this.loadExistingServers();
      
      // Setup cleanup interval
      setInterval(() => {
        this.cleanupStoppedServers();
      }, 60000); // Every minute

      logger.info('Server provisioning service initialized');
    } catch (error) {
      logger.error('Failed to initialize server provisioning service:', error);
    }
  }

  private async loadExistingServers(): Promise<void> {
    try {
      const serverKeys = await RedisService.client.keys('server:*');
      
      for (const key of serverKeys) {
        const serverData = await RedisService.getJSON<ServerInfo>(key);
        if (serverData) {
          this.servers.set(serverData.id, serverData);
          this.usedPorts.add(serverData.port);
        }
      }

      logger.info(`Loaded ${this.servers.size} existing servers`);
    } catch (error) {
      logger.error('Failed to load existing servers:', error);
    }
  }

  private getAvailablePort(): number {
    for (let port = this.portRange.start; port <= this.portRange.end; port++) {
      if (!this.usedPorts.has(port)) {
        this.usedPorts.add(port);
        return port;
      }
    }
    throw new Error('No available ports for server provisioning');
  }

  private releasePort(port: number): void {
    this.usedPorts.delete(port);
  }

  public async provisionServer(serverConfig: ServerConfig): Promise<ServerInfo> {
    const serverId = uuidv4();
    const port = this.getAvailablePort();

    try {
      logger.info(`Provisioning server ${serverId} for match ${serverConfig.matchId}`);

      // Create server info
      const serverInfo: ServerInfo = {
        id: serverId,
        containerId: '',
        matchId: serverConfig.matchId,
        ip: 'localhost', // Will be updated with actual IP
        port,
        status: 'starting',
        createdAt: new Date(),
        config: serverConfig,
      };

      // Store server info
      this.servers.set(serverId, serverInfo);
      await RedisService.setJSON(`server:${serverId}`, serverInfo, 3600); // 1 hour TTL

      // Create Docker container
      const container = await this.createContainer(serverInfo);
      serverInfo.containerId = container.id;

      // Start container
      await container.start();
      
      // Wait for container to be ready
      await this.waitForServerReady(serverInfo);

      // Update status
      serverInfo.status = 'running';
      this.servers.set(serverId, serverInfo);
      await RedisService.setJSON(`server:${serverId}`, serverInfo, 3600);

      // Notify via RabbitMQ
      await RabbitMQService.publish('server.status', {
        type: 'SERVER_PROVISIONED',
        serverId,
        matchId: serverConfig.matchId,
        serverInfo,
      });

      logger.info(`Server ${serverId} provisioned successfully on port ${port}`);
      return serverInfo;

    } catch (error) {
      logger.error(`Failed to provision server ${serverId}:`, error);
      
      // Cleanup on failure
      this.releasePort(port);
      this.servers.delete(serverId);
      await RedisService.del(`server:${serverId}`);
      
      throw error;
    }
  }

  private async createContainer(serverInfo: ServerInfo): Promise<Docker.Container> {
    const { config: serverConfig } = serverInfo;

    const containerConfig = {
      Image: config.cs16ServerImage,
      name: `cs16-server-${serverInfo.id}`,
      Env: [
        `SERVER_NAME=${serverConfig.serverName}`,
        `MAX_PLAYERS=${serverConfig.maxPlayers}`,
        `MAP=${serverConfig.map}`,
        `RCON_PASSWORD=${serverConfig.rconPassword}`,
        `SERVER_PASSWORD=${serverConfig.serverPassword || ''}`,
        `TICKRATE=${serverConfig.tickrate || 100}`,
        `MATCH_ID=${serverConfig.matchId}`,
      ],
      ExposedPorts: {
        '27015/udp': {},
        '27015/tcp': {},
      },
      HostConfig: {
        PortBindings: {
          '27015/udp': [{ HostPort: serverInfo.port.toString() }],
          '27015/tcp': [{ HostPort: serverInfo.port.toString() }],
        },
        Memory: 512 * 1024 * 1024, // 512MB
        CpuShares: 512,
        RestartPolicy: {
          Name: 'no',
        },
      },
      Labels: {
        'cs-arena.service': 'game-server',
        'cs-arena.match-id': serverConfig.matchId,
        'cs-arena.server-id': serverInfo.id,
      },
    };

    return await this.docker.createContainer(containerConfig);
  }

  private async waitForServerReady(serverInfo: ServerInfo, timeout: number = 60000): Promise<void> {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      try {
        const container = this.docker.getContainer(serverInfo.containerId);
        const containerInfo = await container.inspect();
        
        if (containerInfo.State.Running) {
          // Additional check: try to connect to the server port
          // This would require a more sophisticated health check
          logger.info(`Server ${serverInfo.id} is ready`);
          return;
        }
      } catch (error) {
        logger.debug(`Waiting for server ${serverInfo.id} to be ready...`);
      }
      
      await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds
    }
    
    throw new Error(`Server ${serverInfo.id} failed to start within timeout`);
  }

  public async terminateServer(serverId: string): Promise<void> {
    const serverInfo = this.servers.get(serverId);
    
    if (!serverInfo) {
      throw new Error(`Server ${serverId} not found`);
    }

    try {
      logger.info(`Terminating server ${serverId}`);

      serverInfo.status = 'stopping';
      await RedisService.setJSON(`server:${serverId}`, serverInfo, 300); // 5 minutes TTL

      // Stop and remove container
      const container = this.docker.getContainer(serverInfo.containerId);
      
      try {
        await container.stop({ t: 10 }); // 10 seconds grace period
      } catch (error) {
        logger.warn(`Failed to stop container gracefully: ${error}`);
      }
      
      try {
        await container.remove({ force: true });
      } catch (error) {
        logger.warn(`Failed to remove container: ${error}`);
      }

      // Release port
      this.releasePort(serverInfo.port);

      // Update status
      serverInfo.status = 'stopped';
      this.servers.delete(serverId);
      await RedisService.del(`server:${serverId}`);

      // Notify via RabbitMQ
      await RabbitMQService.publish('server.status', {
        type: 'SERVER_TERMINATED',
        serverId,
        matchId: serverInfo.matchId,
      });

      logger.info(`Server ${serverId} terminated successfully`);

    } catch (error) {
      logger.error(`Failed to terminate server ${serverId}:`, error);
      serverInfo.status = 'error';
      throw error;
    }
  }

  public async getServerInfo(serverId: string): Promise<ServerInfo | null> {
    const serverInfo = this.servers.get(serverId);
    
    if (!serverInfo) {
      // Try to load from Redis
      const cachedInfo = await RedisService.getJSON<ServerInfo>(`server:${serverId}`);
      if (cachedInfo) {
        this.servers.set(serverId, cachedInfo);
        return cachedInfo;
      }
      return null;
    }

    // Update status from Docker
    try {
      const container = this.docker.getContainer(serverInfo.containerId);
      const containerInfo = await container.inspect();
      
      if (containerInfo.State.Running) {
        serverInfo.status = 'running';
      } else if (containerInfo.State.Status === 'exited') {
        serverInfo.status = 'stopped';
      }
    } catch (error) {
      serverInfo.status = 'error';
    }

    return serverInfo;
  }

  public async listServers(): Promise<ServerInfo[]> {
    const servers = Array.from(this.servers.values());
    
    // Update statuses
    for (const server of servers) {
      await this.getServerInfo(server.id);
    }
    
    return servers;
  }

  public async getServerStats(): Promise<{
    total: number;
    running: number;
    starting: number;
    stopped: number;
    error: number;
  }> {
    const servers = await this.listServers();
    
    return {
      total: servers.length,
      running: servers.filter(s => s.status === 'running').length,
      starting: servers.filter(s => s.status === 'starting').length,
      stopped: servers.filter(s => s.status === 'stopped').length,
      error: servers.filter(s => s.status === 'error').length,
    };
  }

  private async cleanupStoppedServers(): Promise<void> {
    try {
      const servers = Array.from(this.servers.values());
      const stoppedServers = servers.filter(s => 
        s.status === 'stopped' || 
        (s.status === 'error' && Date.now() - s.createdAt.getTime() > 300000) // 5 minutes
      );

      for (const server of stoppedServers) {
        try {
          await this.terminateServer(server.id);
        } catch (error) {
          logger.error(`Failed to cleanup server ${server.id}:`, error);
        }
      }

      if (stoppedServers.length > 0) {
        logger.info(`Cleaned up ${stoppedServers.length} stopped servers`);
      }
    } catch (error) {
      logger.error('Failed to cleanup stopped servers:', error);
    }
  }

  public async healthCheck(): Promise<boolean> {
    try {
      await this.docker.ping();
      return true;
    } catch (error) {
      logger.error('Docker health check failed:', error);
      return false;
    }
  }

  public async executeCommand(serverId: string, command: string): Promise<string> {
    const serverInfo = this.servers.get(serverId);
    
    if (!serverInfo) {
      throw new Error(`Server ${serverId} not found`);
    }

    try {
      const container = this.docker.getContainer(serverInfo.containerId);
      
      const exec = await container.exec({
        Cmd: ['rcon', serverInfo.config.rconPassword, command],
        AttachStdout: true,
        AttachStderr: true,
      });

      const stream = await exec.start({ hijack: true, stdin: false });
      
      return new Promise((resolve, reject) => {
        let output = '';
        
        stream.on('data', (chunk) => {
          output += chunk.toString();
        });
        
        stream.on('end', () => {
          resolve(output);
        });
        
        stream.on('error', reject);
        
        setTimeout(() => {
          reject(new Error('Command execution timeout'));
        }, 10000); // 10 seconds timeout
      });
    } catch (error) {
      logger.error(`Failed to execute command on server ${serverId}:`, error);
      throw error;
    }
  }
}

// Export singleton instance
export const ServerProvisioningService = ServerProvisioningServiceClass.getInstance();
