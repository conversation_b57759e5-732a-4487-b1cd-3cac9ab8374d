import { Router } from 'express';
import { param, query } from 'express-validator';
import { StatsController } from '@/controllers/statsController';
import { asyncHandler } from '@/middleware/errorHandler';

const router = Router();

// Validation rules
const userIdValidation = [
  param('userId').isUUID(),
];

const timeRangeValidation = [
  query('period').optional().isIn(['day', 'week', 'month', 'year', 'all']),
  query('startDate').optional().isISO8601(),
  query('endDate').optional().isISO8601(),
];

// Routes
router.get('/overview', asyncHandler(StatsController.getOverviewStats));
router.get('/leaderboard', asyncHandler(StatsController.getLeaderboard));
router.get('/rankings', asyncHandler(StatsController.getRankings));

// User stats
router.get('/user/:userId', userIdValidation, asyncHandler(StatsController.getUserStats));
router.get('/user/:userId/detailed', userIdValidation, asyncHandler(StatsController.getDetailedUserStats));
router.get('/user/:userId/history', userIdValidation, timeRangeValidation, asyncHandler(StatsController.getUserStatsHistory));
router.get('/user/:userId/performance', userIdValidation, timeRangeValidation, asyncHandler(StatsController.getUserPerformance));

// Match stats
router.get('/matches/recent', asyncHandler(StatsController.getRecentMatchStats));
router.get('/matches/popular-maps', asyncHandler(StatsController.getPopularMaps));
router.get('/matches/peak-hours', asyncHandler(StatsController.getPeakHours));

// Platform stats
router.get('/platform/overview', asyncHandler(StatsController.getPlatformStats));
router.get('/platform/growth', timeRangeValidation, asyncHandler(StatsController.getGrowthStats));
router.get('/platform/retention', asyncHandler(StatsController.getRetentionStats));

export default router;
