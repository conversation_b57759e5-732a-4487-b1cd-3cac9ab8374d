import { Router } from 'express';
import { body, param, query } from 'express-validator';
import { AdminController } from '@/controllers/adminController';
import { asyncHandler } from '@/middleware/errorHandler';
import { requireAdmin, requireModerator } from '@/middleware/auth';

const router = Router();

// Validation rules
const userIdValidation = [
  param('userId').isUUID(),
];

const banUserValidation = [
  body('reason').isLength({ min: 5, max: 500 }),
  body('duration').optional().isInt({ min: 1 }), // hours
  body('permanent').optional().isBoolean(),
];

const updateUserRoleValidation = [
  body('role').isIn(['PLAYER', 'MODERATOR', 'ADMIN']),
];

const reportIdValidation = [
  param('reportId').isUUID(),
];

const resolveReportValidation = [
  body('action').isIn(['DISMISS', 'WARNING', 'TEMPORARY_BAN', 'PERMANENT_BAN']),
  body('reason').optional().isLength({ max: 500 }),
  body('banDuration').optional().isInt({ min: 1 }),
];

const searchValidation = [
  query('q').optional().isLength({ min: 2, max: 50 }),
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
];

// Dashboard
router.get('/dashboard', requireModerator, asyncHandler(AdminController.getDashboard));
router.get('/stats', requireModerator, asyncHandler(AdminController.getAdminStats));

// User management
router.get('/users', requireModerator, searchValidation, asyncHandler(AdminController.getUsers));
router.get('/users/:userId', requireModerator, userIdValidation, asyncHandler(AdminController.getUser));
router.put('/users/:userId/ban', requireModerator, userIdValidation, banUserValidation, asyncHandler(AdminController.banUser));
router.put('/users/:userId/unban', requireModerator, userIdValidation, asyncHandler(AdminController.unbanUser));
router.put('/users/:userId/role', requireAdmin, userIdValidation, updateUserRoleValidation, asyncHandler(AdminController.updateUserRole));
router.delete('/users/:userId', requireAdmin, userIdValidation, asyncHandler(AdminController.deleteUser));

// Report management
router.get('/reports', requireModerator, asyncHandler(AdminController.getReports));
router.get('/reports/:reportId', requireModerator, reportIdValidation, asyncHandler(AdminController.getReport));
router.put('/reports/:reportId/resolve', requireModerator, reportIdValidation, resolveReportValidation, asyncHandler(AdminController.resolveReport));
router.delete('/reports/:reportId', requireModerator, reportIdValidation, asyncHandler(AdminController.deleteReport));

// Match management
router.get('/matches', requireModerator, asyncHandler(AdminController.getMatches));
router.get('/matches/:matchId', requireModerator, asyncHandler(AdminController.getMatch));
router.put('/matches/:matchId/cancel', requireModerator, asyncHandler(AdminController.cancelMatch));
router.delete('/matches/:matchId', requireAdmin, asyncHandler(AdminController.deleteMatch));

// Server management
router.get('/servers', requireModerator, asyncHandler(AdminController.getServers));
router.get('/servers/stats', requireModerator, asyncHandler(AdminController.getServerStats));
router.post('/servers/cleanup', requireAdmin, asyncHandler(AdminController.cleanupServers));

// Tournament management
router.get('/tournaments', requireModerator, asyncHandler(AdminController.getTournaments));
router.put('/tournaments/:tournamentId/status', requireModerator, asyncHandler(AdminController.updateTournamentStatus));

// Team management
router.get('/teams', requireModerator, asyncHandler(AdminController.getTeams));
router.delete('/teams/:teamId', requireModerator, asyncHandler(AdminController.deleteTeam));

// System management
router.get('/system/health', requireModerator, asyncHandler(AdminController.getSystemHealth));
router.get('/system/logs', requireAdmin, asyncHandler(AdminController.getSystemLogs));
router.post('/system/maintenance', requireAdmin, asyncHandler(AdminController.toggleMaintenance));
router.post('/system/cache/clear', requireAdmin, asyncHandler(AdminController.clearCache));

// Analytics
router.get('/analytics/users', requireModerator, asyncHandler(AdminController.getUserAnalytics));
router.get('/analytics/matches', requireModerator, asyncHandler(AdminController.getMatchAnalytics));
router.get('/analytics/revenue', requireAdmin, asyncHandler(AdminController.getRevenueAnalytics));

// Announcements
router.get('/announcements', requireModerator, asyncHandler(AdminController.getAnnouncements));
router.post('/announcements', requireModerator, asyncHandler(AdminController.createAnnouncement));
router.put('/announcements/:announcementId', requireModerator, asyncHandler(AdminController.updateAnnouncement));
router.delete('/announcements/:announcementId', requireModerator, asyncHandler(AdminController.deleteAnnouncement));

export default router;
