import { Router } from 'express';
import { body, param, query } from 'express-validator';
import { MatchmakingController } from '@/controllers/matchmakingController';
import { asyncHandler } from '@/middleware/errorHandler';

const router = Router();

// Validation rules
const queueValidation = [
  body('gameMode').isIn(['COMPETITIVE', 'CASUAL', 'AIM_TRAINING', 'ONE_VS_ONE']),
  body('maps').isArray({ min: 1, max: 5 }),
  body('maps.*').isIn(['de_dust2', 'de_inferno', 'de_nuke', 'de_train', 'de_cache', 'de_mirage', 'de_cbble', 'de_overpass']),
  body('maxPing').optional().isInt({ min: 10, max: 200 }),
  body('region').optional().isString(),
];

const matchIdValidation = [
  param('matchId').isUUID(),
];

const acceptMatchValidation = [
  body('matchId').isUUID(),
  body('accept').isBoolean(),
];

// Routes
router.post('/queue', queueValidation, asyncHandler(MatchmakingController.joinQueue));
router.delete('/queue', asyncHandler(MatchmakingController.leaveQueue));
router.get('/queue/status', asyncHandler(MatchmakingController.getQueueStatus));
router.get('/queue/stats', asyncHandler(MatchmakingController.getQueueStats));

router.post('/match/accept', acceptMatchValidation, asyncHandler(MatchmakingController.acceptMatch));
router.get('/match/current', asyncHandler(MatchmakingController.getCurrentMatch));
router.get('/match/:matchId', matchIdValidation, asyncHandler(MatchmakingController.getMatch));
router.get('/match/:matchId/status', matchIdValidation, asyncHandler(MatchmakingController.getMatchStatus));

// Match history
router.get('/matches', asyncHandler(MatchmakingController.getMatchHistory));
router.get('/matches/recent', asyncHandler(MatchmakingController.getRecentMatches));

export default router;
