import { PrismaClient } from '@prisma/client';
import { config } from '@/config/config';
import { logger } from '@/utils/logger';

class DatabaseServiceClass {
  private static instance: DatabaseServiceClass;
  private prisma: PrismaClient;

  private constructor() {
    this.prisma = new PrismaClient({
      log: config.nodeEnv === 'development' ? ['query', 'info', 'warn', 'error'] : ['error'],
      datasources: {
        db: {
          url: config.databaseUrl,
        },
      },
    });

    // Add query logging middleware
    this.prisma.$use(async (params, next) => {
      const before = Date.now();
      const result = await next(params);
      const after = Date.now();
      
      if (config.nodeEnv === 'development') {
        logger.debug(`Query ${params.model}.${params.action} took ${after - before}ms`);
      }
      
      return result;
    });
  }

  public static getInstance(): DatabaseServiceClass {
    if (!DatabaseServiceClass.instance) {
      DatabaseServiceClass.instance = new DatabaseServiceClass();
    }
    return DatabaseServiceClass.instance;
  }

  public async initialize(): Promise<void> {
    try {
      await this.prisma.$connect();
      logger.info('Database connected successfully');
    } catch (error) {
      logger.error('Failed to connect to database:', error);
      throw error;
    }
  }

  public async disconnect(): Promise<void> {
    try {
      await this.prisma.$disconnect();
      logger.info('Database disconnected successfully');
    } catch (error) {
      logger.error('Failed to disconnect from database:', error);
      throw error;
    }
  }

  public async healthCheck(): Promise<boolean> {
    try {
      await this.prisma.$queryRaw`SELECT 1`;
      return true;
    } catch (error) {
      logger.error('Database health check failed:', error);
      return false;
    }
  }

  // Expose Prisma client for direct access
  public get client(): PrismaClient {
    return this.prisma;
  }

  // Convenience getters for models
  public get user() {
    return this.prisma.user;
  }

  public get userProfile() {
    return this.prisma.userProfile;
  }

  public get userStats() {
    return this.prisma.userStats;
  }

  public get team() {
    return this.prisma.team;
  }

  public get teamMember() {
    return this.prisma.teamMember;
  }

  public get match() {
    return this.prisma.match;
  }

  public get matchPlayer() {
    return this.prisma.matchPlayer;
  }

  public get matchRound() {
    return this.prisma.matchRound;
  }

  public get tournament() {
    return this.prisma.tournament;
  }

  public get tournamentParticipant() {
    return this.prisma.tournamentParticipant;
  }

  public get tournamentTeam() {
    return this.prisma.tournamentTeam;
  }

  public get tournamentBracket() {
    return this.prisma.tournamentBracket;
  }

  public get notification() {
    return this.prisma.notification;
  }

  public get report() {
    return this.prisma.report;
  }

  public get friendship() {
    return this.prisma.friendship;
  }

  public get chatMessage() {
    return this.prisma.chatMessage;
  }

  // Transaction helper
  public async transaction<T>(fn: (prisma: PrismaClient) => Promise<T>): Promise<T> {
    return this.prisma.$transaction(fn);
  }

  // Batch operations
  public async batchUpdate(operations: any[]): Promise<any> {
    return this.prisma.$transaction(operations);
  }

  // Raw query helper
  public async rawQuery(query: string, params?: any[]): Promise<any> {
    return this.prisma.$queryRawUnsafe(query, ...(params || []));
  }

  // Execute raw SQL
  public async executeRaw(query: string, params?: any[]): Promise<any> {
    return this.prisma.$executeRawUnsafe(query, ...(params || []));
  }
}

// Export singleton instance
export const DatabaseService = DatabaseServiceClass.getInstance();
