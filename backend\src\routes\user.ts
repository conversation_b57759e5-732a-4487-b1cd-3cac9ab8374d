import { Router } from 'express';
import { body, param, query } from 'express-validator';
import { UserController } from '@/controllers/userController';
import { asyncHandler } from '@/middleware/errorHandler';
import { requireModerator } from '@/middleware/auth';

const router = Router();

// Validation rules
const updateProfileValidation = [
  body('displayName').optional().isLength({ min: 2, max: 50 }),
  body('firstName').optional().isLength({ min: 1, max: 50 }),
  body('lastName').optional().isLength({ min: 1, max: 50 }),
  body('country').optional().isLength({ min: 2, max: 2 }),
  body('timezone').optional().isString(),
  body('language').optional().isIn(['en', 'pt', 'es']),
  body('bio').optional().isLength({ max: 500 }),
  body('website').optional().isURL(),
  body('twitterHandle').optional().matches(/^@?[a-zA-Z0-9_]+$/),
  body('twitchHandle').optional().matches(/^[a-zA-Z0-9_]+$/),
  body('discordTag').optional().matches(/^.{3,32}#[0-9]{4}$/),
];

const userIdValidation = [
  param('userId').isUUID(),
];

const searchValidation = [
  query('q').isLength({ min: 2, max: 50 }),
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 50 }),
];

// Routes
router.get('/search', searchValidation, asyncHandler(UserController.searchUsers));
router.get('/leaderboard', asyncHandler(UserController.getLeaderboard));
router.get('/online', asyncHandler(UserController.getOnlineUsers));

router.get('/:userId', userIdValidation, asyncHandler(UserController.getUserById));
router.get('/:userId/stats', userIdValidation, asyncHandler(UserController.getUserStats));
router.get('/:userId/matches', userIdValidation, asyncHandler(UserController.getUserMatches));
router.get('/:userId/achievements', userIdValidation, asyncHandler(UserController.getUserAchievements));

// Profile management
router.put('/profile', updateProfileValidation, asyncHandler(UserController.updateProfile));
router.post('/avatar', asyncHandler(UserController.uploadAvatar));
router.delete('/avatar', asyncHandler(UserController.deleteAvatar));

// Settings
router.get('/settings', asyncHandler(UserController.getSettings));
router.put('/settings', asyncHandler(UserController.updateSettings));

// Friends
router.get('/friends', asyncHandler(UserController.getFriends));
router.post('/friends/:userId', userIdValidation, asyncHandler(UserController.sendFriendRequest));
router.put('/friends/:userId', userIdValidation, asyncHandler(UserController.acceptFriendRequest));
router.delete('/friends/:userId', userIdValidation, asyncHandler(UserController.removeFriend));
router.get('/friends/requests', asyncHandler(UserController.getFriendRequests));

// Notifications
router.get('/notifications', asyncHandler(UserController.getNotifications));
router.put('/notifications/:notificationId/read', asyncHandler(UserController.markNotificationAsRead));
router.put('/notifications/read-all', asyncHandler(UserController.markAllNotificationsAsRead));
router.delete('/notifications/:notificationId', asyncHandler(UserController.deleteNotification));

// Reports
router.post('/report/:userId', userIdValidation, asyncHandler(UserController.reportUser));

// Admin routes
router.get('/admin/users', requireModerator, asyncHandler(UserController.getAllUsers));
router.put('/admin/users/:userId/ban', requireModerator, userIdValidation, asyncHandler(UserController.banUser));
router.put('/admin/users/:userId/unban', requireModerator, userIdValidation, asyncHandler(UserController.unbanUser));
router.put('/admin/users/:userId/role', requireModerator, userIdValidation, asyncHandler(UserController.updateUserRole));

export default router;
