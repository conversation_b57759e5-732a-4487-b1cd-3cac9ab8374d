#!/bin/bash

# CS 1.6 Server Startup Script
# This script configures and starts a Counter-Strike 1.6 server

set -e

echo "Starting CS 1.6 Arena Server..."
echo "Server Name: $SERVER_NAME"
echo "Max Players: $MAX_PLAYERS"
echo "Map: $MAP"
echo "Tickrate: $TICKRATE"

# Set default values if not provided
SERVER_NAME=${SERVER_NAME:-"CS Arena Server"}
MAX_PLAYERS=${MAX_PLAYERS:-10}
MAP=${MAP:-de_dust2}
RCON_PASSWORD=${RCON_PASSWORD:-cs_arena_rcon}
TICKRATE=${TICKRATE:-100}

# Create server.cfg from template
cat > /home/<USER>/hlds/cstrike/server.cfg << EOF
// CS Arena Server Configuration
// Generated automatically - do not edit manually

// Server Information
hostname "$SERVER_NAME"
sv_password "$SERVER_PASSWORD"
rcon_password "$RCON_PASSWORD"

// Game Settings
mp_timelimit 0
mp_winlimit 0
mp_maxrounds 30
mp_startmoney 800
mp_c4timer 35
mp_freezetime 6
mp_roundtime 1.75
mp_buytime 0.25
mp_autokick 0
mp_autoteambalance 0
mp_limitteams 0
mp_friendlyfire 1
mp_tkpunish 0
mp_hostagepenalty 0
mp_forcecamera 0
mp_forcechasecam 0
mp_fadetoblack 0

// Server Performance
sys_ticrate $TICKRATE
fps_max 1000
sv_maxrate 25000
sv_minrate 15000
sv_maxupdaterate 101
sv_minupdaterate 20
sv_maxcmdrate 101
sv_mincmdrate 20

// Anti-Cheat Settings
sv_cheats 0
allow_spectators 1
mp_allowspectators 1

// Logging
log on
sv_logbans 1
sv_logecho 1
sv_logfile 1
sv_log_onefile 0

// Network Settings
sv_lan 0
sv_region 3
sv_contact "<EMAIL>"

// Map Cycle
mapcyclefile mapcycle.txt

// Execute additional configs
exec banned.cfg
exec listip.cfg

echo "CS Arena Server Started Successfully"
EOF

# Create mapcycle.txt
cat > /home/<USER>/hlds/cstrike/mapcycle.txt << EOF
de_dust2
de_inferno
de_nuke
de_train
de_cache
de_mirage
de_cbble
de_overpass
EOF

# Create banned.cfg and listip.cfg if they don't exist
touch /home/<USER>/hlds/cstrike/banned.cfg
touch /home/<USER>/hlds/cstrike/listip.cfg

# Copy any custom configurations
if [ -d "/home/<USER>/configs" ]; then
    echo "Copying custom configurations..."
    cp -r /home/<USER>/configs/* /home/<USER>/hlds/cstrike/ 2>/dev/null || true
fi

# Start the server
echo "Launching CS 1.6 server..."
cd /home/<USER>/hlds

# Start server with proper parameters
exec ./hlds_run \
    -game cstrike \
    -port 27015 \
    -maxplayers $MAX_PLAYERS \
    +map $MAP \
    -pingboost 3 \
    -sys_ticrate $TICKRATE \
    +rcon_password "$RCON_PASSWORD" \
    +hostname "$SERVER_NAME" \
    +sv_password "$SERVER_PASSWORD" \
    -nomaster \
    -secure
