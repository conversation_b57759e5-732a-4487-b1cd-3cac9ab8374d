import amqp, { Connection, Channel, Message } from 'amqplib';
import { config } from '@/config/config';
import { logger } from '@/utils/logger';

interface QueueOptions {
  durable?: boolean;
  exclusive?: boolean;
  autoDelete?: boolean;
  arguments?: any;
}

interface PublishOptions {
  persistent?: boolean;
  priority?: number;
  expiration?: string;
  headers?: any;
}

class RabbitMQServiceClass {
  private static instance: RabbitMQServiceClass;
  private connection: Connection | null = null;
  private channel: Channel | null = null;
  private isConnected: boolean = false;

  private constructor() {}

  public static getInstance(): RabbitMQServiceClass {
    if (!RabbitMQServiceClass.instance) {
      RabbitMQServiceClass.instance = new RabbitMQServiceClass();
    }
    return RabbitMQServiceClass.instance;
  }

  public async initialize(): Promise<void> {
    try {
      this.connection = await amqp.connect(config.rabbitmqUrl);
      this.channel = await this.connection.createChannel();

      this.connection.on('error', (error) => {
        logger.error('RabbitMQ connection error:', error);
        this.isConnected = false;
      });

      this.connection.on('close', () => {
        logger.warn('RabbitMQ connection closed');
        this.isConnected = false;
      });

      this.channel.on('error', (error) => {
        logger.error('RabbitMQ channel error:', error);
      });

      this.channel.on('close', () => {
        logger.warn('RabbitMQ channel closed');
      });

      // Setup default exchanges and queues
      await this.setupDefaultQueues();

      this.isConnected = true;
      logger.info('RabbitMQ connected successfully');
    } catch (error) {
      logger.error('Failed to connect to RabbitMQ:', error);
      throw error;
    }
  }

  public async disconnect(): Promise<void> {
    try {
      if (this.channel) {
        await this.channel.close();
      }
      if (this.connection) {
        await this.connection.close();
      }
      this.isConnected = false;
      logger.info('RabbitMQ disconnected successfully');
    } catch (error) {
      logger.error('Failed to disconnect from RabbitMQ:', error);
      throw error;
    }
  }

  public async healthCheck(): Promise<boolean> {
    try {
      if (!this.channel) return false;
      await this.channel.checkQueue('health-check');
      return true;
    } catch (error) {
      logger.error('RabbitMQ health check failed:', error);
      return false;
    }
  }

  private async setupDefaultQueues(): Promise<void> {
    if (!this.channel) throw new Error('Channel not initialized');

    // Matchmaking queues
    await this.channel.assertQueue('matchmaking.queue', { durable: true });
    await this.channel.assertQueue('matchmaking.result', { durable: true });

    // Server management queues
    await this.channel.assertQueue('server.provision', { durable: true });
    await this.channel.assertQueue('server.terminate', { durable: true });
    await this.channel.assertQueue('server.status', { durable: true });

    // Notification queues
    await this.channel.assertQueue('notifications.email', { durable: true });
    await this.channel.assertQueue('notifications.push', { durable: true });
    await this.channel.assertQueue('notifications.websocket', { durable: true });

    // Statistics queues
    await this.channel.assertQueue('stats.update', { durable: true });
    await this.channel.assertQueue('stats.calculate', { durable: true });

    // Anti-cheat queues
    await this.channel.assertQueue('anticheat.scan', { durable: true });
    await this.channel.assertQueue('anticheat.report', { durable: true });

    logger.info('Default RabbitMQ queues setup completed');
  }

  public async assertQueue(queue: string, options: QueueOptions = {}): Promise<void> {
    if (!this.channel) throw new Error('Channel not initialized');
    
    const defaultOptions = {
      durable: true,
      exclusive: false,
      autoDelete: false,
      ...options,
    };

    await this.channel.assertQueue(queue, defaultOptions);
  }

  public async publish(queue: string, message: any, options: PublishOptions = {}): Promise<boolean> {
    try {
      if (!this.channel) throw new Error('Channel not initialized');

      await this.assertQueue(queue);

      const messageBuffer = Buffer.from(JSON.stringify(message));
      const publishOptions = {
        persistent: true,
        timestamp: Date.now(),
        ...options,
      };

      const result = this.channel.sendToQueue(queue, messageBuffer, publishOptions);
      
      if (result) {
        logger.debug(`Message published to queue ${queue}`);
      } else {
        logger.warn(`Failed to publish message to queue ${queue}`);
      }

      return result;
    } catch (error) {
      logger.error(`Error publishing to queue ${queue}:`, error);
      return false;
    }
  }

  public async consume(
    queue: string,
    handler: (message: any) => Promise<void>,
    options: { noAck?: boolean } = {}
  ): Promise<void> {
    if (!this.channel) throw new Error('Channel not initialized');

    await this.assertQueue(queue);

    await this.channel.consume(
      queue,
      async (msg: Message | null) => {
        if (!msg) return;

        try {
          const content = JSON.parse(msg.content.toString());
          await handler(content);

          if (!options.noAck) {
            this.channel!.ack(msg);
          }
        } catch (error) {
          logger.error(`Error processing message from queue ${queue}:`, error);
          
          if (!options.noAck) {
            this.channel!.nack(msg, false, false); // Don't requeue failed messages
          }
        }
      },
      { noAck: options.noAck || false }
    );

    logger.info(`Consumer setup for queue ${queue}`);
  }

  // Specific queue publishers
  public async publishMatchmakingRequest(data: {
    userId: string;
    gameMode: string;
    maps: string[];
    maxPing: number;
  }): Promise<boolean> {
    return this.publish('matchmaking.queue', {
      type: 'MATCHMAKING_REQUEST',
      data,
      timestamp: new Date().toISOString(),
    });
  }

  public async publishServerProvisionRequest(data: {
    matchId: string;
    map: string;
    players: string[];
    config: any;
  }): Promise<boolean> {
    return this.publish('server.provision', {
      type: 'SERVER_PROVISION',
      data,
      timestamp: new Date().toISOString(),
    });
  }

  public async publishServerTerminateRequest(data: {
    serverId: string;
    matchId: string;
  }): Promise<boolean> {
    return this.publish('server.terminate', {
      type: 'SERVER_TERMINATE',
      data,
      timestamp: new Date().toISOString(),
    });
  }

  public async publishNotification(data: {
    userId: string;
    type: string;
    title: string;
    message: string;
    data?: any;
  }): Promise<boolean> {
    return this.publish('notifications.push', {
      type: 'NOTIFICATION',
      data,
      timestamp: new Date().toISOString(),
    });
  }

  public async publishStatsUpdate(data: {
    matchId: string;
    players: any[];
    matchResult: any;
  }): Promise<boolean> {
    return this.publish('stats.update', {
      type: 'STATS_UPDATE',
      data,
      timestamp: new Date().toISOString(),
    });
  }

  public async publishAntiCheatScan(data: {
    userId: string;
    matchId: string;
    suspiciousActivity: any;
  }): Promise<boolean> {
    return this.publish('anticheat.scan', {
      type: 'ANTICHEAT_SCAN',
      data,
      timestamp: new Date().toISOString(),
    });
  }

  // Exchange operations
  public async assertExchange(exchange: string, type: string, options: any = {}): Promise<void> {
    if (!this.channel) throw new Error('Channel not initialized');
    
    await this.channel.assertExchange(exchange, type, {
      durable: true,
      ...options,
    });
  }

  public async publishToExchange(
    exchange: string,
    routingKey: string,
    message: any,
    options: PublishOptions = {}
  ): Promise<boolean> {
    try {
      if (!this.channel) throw new Error('Channel not initialized');

      const messageBuffer = Buffer.from(JSON.stringify(message));
      const publishOptions = {
        persistent: true,
        timestamp: Date.now(),
        ...options,
      };

      const result = this.channel.publish(exchange, routingKey, messageBuffer, publishOptions);
      
      if (result) {
        logger.debug(`Message published to exchange ${exchange} with routing key ${routingKey}`);
      }

      return result;
    } catch (error) {
      logger.error(`Error publishing to exchange ${exchange}:`, error);
      return false;
    }
  }

  public get isHealthy(): boolean {
    return this.isConnected;
  }
}

// Export singleton instance
export const RabbitMQService = RabbitMQServiceClass.getInstance();
