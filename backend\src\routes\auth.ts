import { Router } from 'express';
import { body } from 'express-validator';
import { AuthController } from '@/controllers/authController';
import { asyncHandler } from '@/middleware/errorHandler';
import { authMiddleware } from '@/middleware/auth';

const router = Router();

// Validation rules
const registerValidation = [
  body('email').isEmail().normalizeEmail(),
  body('username').isLength({ min: 3, max: 20 }).matches(/^[a-zA-Z0-9_]+$/),
  body('password').isLength({ min: 8 }).matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/),
  body('displayName').optional().isLength({ min: 2, max: 50 }),
];

const loginValidation = [
  body('email').isEmail().normalizeEmail(),
  body('password').notEmpty(),
];

const forgotPasswordValidation = [
  body('email').isEmail().normalizeEmail(),
];

const resetPasswordValidation = [
  body('token').notEmpty(),
  body('password').isLength({ min: 8 }).matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/),
];

const changePasswordValidation = [
  body('currentPassword').notEmpty(),
  body('newPassword').isLength({ min: 8 }).matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/),
];

// Routes
router.post('/register', registerValidation, asyncHandler(AuthController.register));
router.post('/login', loginValidation, asyncHandler(AuthController.login));
router.post('/logout', authMiddleware, asyncHandler(AuthController.logout));
router.post('/refresh', asyncHandler(AuthController.refreshToken));
router.post('/forgot-password', forgotPasswordValidation, asyncHandler(AuthController.forgotPassword));
router.post('/reset-password', resetPasswordValidation, asyncHandler(AuthController.resetPassword));
router.post('/change-password', authMiddleware, changePasswordValidation, asyncHandler(AuthController.changePassword));
router.post('/verify-email', asyncHandler(AuthController.verifyEmail));
router.post('/resend-verification', authMiddleware, asyncHandler(AuthController.resendVerification));

// OAuth routes
router.get('/google', asyncHandler(AuthController.googleAuth));
router.get('/google/callback', asyncHandler(AuthController.googleCallback));
router.get('/steam', asyncHandler(AuthController.steamAuth));
router.get('/steam/callback', asyncHandler(AuthController.steamCallback));

// Profile routes
router.get('/me', authMiddleware, asyncHandler(AuthController.getProfile));
router.put('/me', authMiddleware, asyncHandler(AuthController.updateProfile));

export default router;
