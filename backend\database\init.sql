-- CS Arena Database Initialization Script
-- This script sets up the initial database configuration

-- Create database if it doesn't exist
-- Note: This is handled by Docker Compose environment variables

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gin";

-- Create custom types for better performance
DO $$ BEGIN
    CREATE TYPE user_role AS ENUM ('PLAYER', 'MODERATOR', 'ADMIN', 'SUPER_ADMIN');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE rank_type AS ENUM ('BRONZE', 'SILVER', 'GOLD', 'DIAMOND', 'ELITE');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE match_status AS ENUM ('WAITING', 'STARTING', 'LIVE', 'PAUSED', 'FINISHED', 'CANCELLED');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE game_mode AS ENUM ('COMPETITIVE', 'CASUAL', 'AIM_TRAINING', 'ONE_VS_ONE');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Create indexes for better performance (will be created by Prisma migrations)
-- These are just examples of what we'll need

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Function to calculate ELO rating
CREATE OR REPLACE FUNCTION calculate_elo_change(
    player_rating INTEGER,
    opponent_rating INTEGER,
    player_won BOOLEAN,
    k_factor INTEGER DEFAULT 32
) RETURNS INTEGER AS $$
DECLARE
    expected_score DECIMAL;
    actual_score INTEGER;
    rating_change INTEGER;
BEGIN
    -- Calculate expected score
    expected_score := 1.0 / (1.0 + POWER(10, (opponent_rating - player_rating) / 400.0));
    
    -- Actual score (1 for win, 0 for loss)
    actual_score := CASE WHEN player_won THEN 1 ELSE 0 END;
    
    -- Calculate rating change
    rating_change := ROUND(k_factor * (actual_score - expected_score));
    
    RETURN rating_change;
END;
$$ LANGUAGE plpgsql;

-- Function to get user rank based on ELO
CREATE OR REPLACE FUNCTION get_rank_from_elo(elo_rating INTEGER) 
RETURNS TEXT AS $$
BEGIN
    CASE 
        WHEN elo_rating >= 2000 THEN RETURN 'ELITE';
        WHEN elo_rating >= 1600 THEN RETURN 'DIAMOND';
        WHEN elo_rating >= 1200 THEN RETURN 'GOLD';
        WHEN elo_rating >= 800 THEN RETURN 'SILVER';
        ELSE RETURN 'BRONZE';
    END CASE;
END;
$$ LANGUAGE plpgsql;

-- Function to update user rank based on ELO
CREATE OR REPLACE FUNCTION update_user_rank()
RETURNS TRIGGER AS $$
BEGIN
    NEW.rank := get_rank_from_elo(NEW.elo_rating)::rank_type;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a view for leaderboard
CREATE OR REPLACE VIEW leaderboard AS
SELECT 
    u.id,
    u.username,
    u.display_name,
    u.avatar,
    us.elo_rating,
    us.rank,
    us.total_matches,
    us.wins,
    us.losses,
    us.draws,
    CASE 
        WHEN us.total_matches > 0 THEN ROUND((us.wins::DECIMAL / us.total_matches::DECIMAL) * 100, 2)
        ELSE 0
    END as win_rate,
    us.kills,
    us.deaths,
    CASE 
        WHEN us.deaths > 0 THEN ROUND(us.kills::DECIMAL / us.deaths::DECIMAL, 2)
        ELSE us.kills::DECIMAL
    END as kd_ratio,
    us.mvp_count,
    us.updated_at
FROM users u
JOIN user_stats us ON u.id = us.user_id
WHERE u.is_active = true AND u.is_banned = false
ORDER BY us.elo_rating DESC, us.wins DESC;

-- Create a view for match statistics
CREATE OR REPLACE VIEW match_statistics AS
SELECT 
    m.id as match_id,
    m.map,
    m.game_mode,
    m.status,
    m.score_team_a,
    m.score_team_b,
    m.winner_team,
    m.started_at,
    m.ended_at,
    EXTRACT(EPOCH FROM (m.ended_at - m.started_at)) / 60 as duration_minutes,
    COUNT(mp.id) as player_count,
    AVG(mp.kills) as avg_kills,
    AVG(mp.deaths) as avg_deaths,
    SUM(mp.kills) as total_kills,
    SUM(mp.deaths) as total_deaths
FROM matches m
LEFT JOIN match_players mp ON m.id = mp.match_id
WHERE m.status = 'FINISHED'
GROUP BY m.id, m.map, m.game_mode, m.status, m.score_team_a, m.score_team_b, 
         m.winner_team, m.started_at, m.ended_at;

-- Create indexes for common queries (examples)
-- Note: Prisma will handle most of these, but we can add custom ones here

-- Index for user search
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_search 
-- ON users USING gin(to_tsvector('english', username || ' ' || display_name));

-- Index for match queries
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_matches_status_created 
-- ON matches(status, created_at DESC);

-- Index for leaderboard queries
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_stats_elo_wins 
-- ON user_stats(elo_rating DESC, wins DESC);

-- Index for match player stats
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_match_players_user_match 
-- ON match_players(user_id, match_id);

-- Grant permissions
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO cs_arena_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO cs_arena_user;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO cs_arena_user;

-- Log initialization
INSERT INTO public.system_logs (level, message, created_at) 
VALUES ('INFO', 'Database initialized successfully', NOW())
ON CONFLICT DO NOTHING;
