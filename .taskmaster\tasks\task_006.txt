# Task ID: 6
# Title: Develop Matchmaking Algorithm
# Status: done
# Dependencies: 2, 3
# Priority: high
# Description: Create the core matchmaking algorithm based on ELO rating, ping, and player preferences.
# Details:
1. Implement ELO rating system adapted for CS 1.6
2. Create algorithm for matching players based on skill level
3. Incorporate ping/region preferences in matching
4. Implement map selection logic
5. Create queue management system
6. Develop timeout and acceptance mechanism
7. Implement team balancing logic

Algorithm should consider:
- Player skill (ELO rating)
- Ping/region preferences
- Map preferences
- Queue time (gradually expanding search criteria)
- Team balance

Use Redis for queue management and real-time updates.

# Test Strategy:
Create unit tests with various player distributions. Test edge cases like high skill disparity. Simulate different queue scenarios. Measure match quality and queue times.
