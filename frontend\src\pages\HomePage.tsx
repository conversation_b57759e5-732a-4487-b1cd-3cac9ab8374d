import React from 'react';
import { Box, Container, <PERSON>po<PERSON>, <PERSON>ton, Grid, Card, CardContent, CardMedia } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { motion } from 'framer-motion';

// Icons
import SportsEsportsIcon from '@mui/icons-material/SportsEsports';
import EmojiEventsIcon from '@mui/icons-material/EmojiEvents';
import GroupIcon from '@mui/icons-material/Group';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';

const HomePage: React.FC = () => {
  const navigate = useNavigate();

  const features = [
    {
      icon: <SportsEsportsIcon sx={{ fontSize: 48, color: 'primary.main' }} />,
      title: 'Competitive Matchmaking',
      description: 'Join ranked matches with players of similar skill level. Our advanced ELO system ensures fair and competitive games.',
    },
    {
      icon: <EmojiEventsIcon sx={{ fontSize: 48, color: 'primary.main' }} />,
      title: 'Tournaments',
      description: 'Participate in daily, weekly, and monthly tournaments. Compete for prizes and climb the leaderboards.',
    },
    {
      icon: <GroupIcon sx={{ fontSize: 48, color: 'primary.main' }} />,
      title: 'Team Management',
      description: 'Create or join teams, manage rosters, and compete in team-based tournaments and leagues.',
    },
    {
      icon: <TrendingUpIcon sx={{ fontSize: 48, color: 'primary.main' }} />,
      title: 'Statistics & Analytics',
      description: 'Track your performance with detailed statistics, match history, and performance analytics.',
    },
  ];

  const maps = [
    { name: 'de_dust2', image: '/images/maps/dust2.jpg' },
    { name: 'de_inferno', image: '/images/maps/inferno.jpg' },
    { name: 'de_nuke', image: '/images/maps/nuke.jpg' },
    { name: 'de_train', image: '/images/maps/train.jpg' },
  ];

  return (
    <>
      <Helmet>
        <title>CS 1.6 Arena - Competitive Matchmaking Platform</title>
        <meta name="description" content="The ultimate competitive platform for Counter-Strike 1.6. Join thousands of players in ranked matches with professional-grade servers." />
      </Helmet>

      <Box sx={{ minHeight: '100vh', background: 'linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%)' }}>
        {/* Hero Section */}
        <Box
          sx={{
            background: 'linear-gradient(135deg, rgba(255, 107, 53, 0.1) 0%, rgba(44, 62, 80, 0.1) 100%)',
            py: { xs: 8, md: 12 },
            position: 'relative',
            overflow: 'hidden',
          }}
        >
          <Container maxWidth="lg">
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              <Grid container spacing={4} alignItems="center">
                <Grid item xs={12} md={6}>
                  <Typography
                    variant="h1"
                    component="h1"
                    sx={{
                      fontSize: { xs: '2.5rem', md: '4rem' },
                      fontWeight: 700,
                      mb: 2,
                      background: 'linear-gradient(45deg, #ff6b35 30%, #ff9566 90%)',
                      backgroundClip: 'text',
                      WebkitBackgroundClip: 'text',
                      WebkitTextFillColor: 'transparent',
                    }}
                  >
                    CS 1.6 Arena
                  </Typography>
                  <Typography
                    variant="h4"
                    component="h2"
                    sx={{
                      fontSize: { xs: '1.25rem', md: '1.75rem' },
                      mb: 3,
                      color: 'text.secondary',
                      fontWeight: 400,
                    }}
                  >
                    The Ultimate Competitive Platform for Counter-Strike 1.6
                  </Typography>
                  <Typography
                    variant="body1"
                    sx={{
                      fontSize: '1.125rem',
                      mb: 4,
                      color: 'text.secondary',
                      lineHeight: 1.6,
                    }}
                  >
                    Experience the nostalgia of classic Counter-Strike with modern competitive features. 
                    Join thousands of players in ranked matches, tournaments, and team competitions.
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                    <Button
                      variant="contained"
                      size="large"
                      onClick={() => navigate('/register')}
                      sx={{
                        px: 4,
                        py: 1.5,
                        fontSize: '1.1rem',
                        background: 'linear-gradient(45deg, #ff6b35 30%, #ff9566 90%)',
                        '&:hover': {
                          background: 'linear-gradient(45deg, #e55a2b 30%, #e5845c 90%)',
                        },
                      }}
                    >
                      Start Playing
                    </Button>
                    <Button
                      variant="outlined"
                      size="large"
                      onClick={() => navigate('/login')}
                      sx={{
                        px: 4,
                        py: 1.5,
                        fontSize: '1.1rem',
                        borderColor: 'primary.main',
                        color: 'primary.main',
                        '&:hover': {
                          borderColor: 'primary.light',
                          backgroundColor: 'rgba(255, 107, 53, 0.1)',
                        },
                      }}
                    >
                      Login
                    </Button>
                  </Box>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Box
                    sx={{
                      position: 'relative',
                      textAlign: 'center',
                    }}
                  >
                    <motion.div
                      animate={{ y: [0, -10, 0] }}
                      transition={{ duration: 3, repeat: Infinity }}
                    >
                      <Box
                        component="img"
                        src="/images/cs16-hero.png"
                        alt="Counter-Strike 1.6"
                        sx={{
                          width: '100%',
                          maxWidth: 500,
                          height: 'auto',
                          filter: 'drop-shadow(0 10px 30px rgba(0, 0, 0, 0.5))',
                        }}
                      />
                    </motion.div>
                  </Box>
                </Grid>
              </Grid>
            </motion.div>
          </Container>
        </Box>

        {/* Features Section */}
        <Container maxWidth="lg" sx={{ py: 8 }}>
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <Typography
              variant="h2"
              component="h2"
              textAlign="center"
              sx={{ mb: 6, fontSize: { xs: '2rem', md: '3rem' } }}
            >
              Why Choose CS Arena?
            </Typography>
            <Grid container spacing={4}>
              {features.map((feature, index) => (
                <Grid item xs={12} sm={6} md={3} key={index}>
                  <motion.div
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    viewport={{ once: true }}
                  >
                    <Card
                      sx={{
                        height: '100%',
                        background: 'rgba(255, 255, 255, 0.05)',
                        backdropFilter: 'blur(10px)',
                        border: '1px solid rgba(255, 255, 255, 0.1)',
                        transition: 'transform 0.3s ease-in-out',
                        '&:hover': {
                          transform: 'translateY(-5px)',
                        },
                      }}
                    >
                      <CardContent sx={{ textAlign: 'center', p: 3 }}>
                        <Box sx={{ mb: 2 }}>
                          {feature.icon}
                        </Box>
                        <Typography variant="h6" component="h3" sx={{ mb: 2, fontWeight: 600 }}>
                          {feature.title}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {feature.description}
                        </Typography>
                      </CardContent>
                    </Card>
                  </motion.div>
                </Grid>
              ))}
            </Grid>
          </motion.div>
        </Container>

        {/* Maps Section */}
        <Box sx={{ py: 8, backgroundColor: 'rgba(255, 255, 255, 0.02)' }}>
          <Container maxWidth="lg">
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <Typography
                variant="h2"
                component="h2"
                textAlign="center"
                sx={{ mb: 6, fontSize: { xs: '2rem', md: '3rem' } }}
              >
                Classic Maps
              </Typography>
              <Grid container spacing={3}>
                {maps.map((map, index) => (
                  <Grid item xs={12} sm={6} md={3} key={index}>
                    <motion.div
                      initial={{ opacity: 0, scale: 0.9 }}
                      whileInView={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.6, delay: index * 0.1 }}
                      viewport={{ once: true }}
                    >
                      <Card
                        sx={{
                          background: 'rgba(255, 255, 255, 0.05)',
                          backdropFilter: 'blur(10px)',
                          border: '1px solid rgba(255, 255, 255, 0.1)',
                          transition: 'transform 0.3s ease-in-out',
                          '&:hover': {
                            transform: 'scale(1.05)',
                          },
                        }}
                      >
                        <CardMedia
                          component="img"
                          height="140"
                          image={map.image}
                          alt={map.name}
                          sx={{
                            filter: 'brightness(0.8)',
                            transition: 'filter 0.3s ease-in-out',
                            '&:hover': {
                              filter: 'brightness(1)',
                            },
                          }}
                        />
                        <CardContent>
                          <Typography
                            variant="h6"
                            component="h3"
                            textAlign="center"
                            sx={{ fontFamily: 'monospace', color: 'primary.main' }}
                          >
                            {map.name}
                          </Typography>
                        </CardContent>
                      </Card>
                    </motion.div>
                  </Grid>
                ))}
              </Grid>
            </motion.div>
          </Container>
        </Box>

        {/* CTA Section */}
        <Container maxWidth="lg" sx={{ py: 8 }}>
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <Box
              sx={{
                textAlign: 'center',
                background: 'linear-gradient(135deg, rgba(255, 107, 53, 0.1) 0%, rgba(44, 62, 80, 0.1) 100%)',
                borderRadius: 4,
                p: 6,
                border: '1px solid rgba(255, 107, 53, 0.2)',
              }}
            >
              <Typography
                variant="h3"
                component="h2"
                sx={{ mb: 3, fontSize: { xs: '1.75rem', md: '2.5rem' } }}
              >
                Ready to Dominate?
              </Typography>
              <Typography
                variant="body1"
                sx={{ mb: 4, fontSize: '1.125rem', color: 'text.secondary' }}
              >
                Join the arena and prove your skills against the best CS 1.6 players.
                Create your account now and start your competitive journey.
              </Typography>
              <Button
                variant="contained"
                size="large"
                onClick={() => navigate('/register')}
                sx={{
                  px: 6,
                  py: 2,
                  fontSize: '1.2rem',
                  background: 'linear-gradient(45deg, #ff6b35 30%, #ff9566 90%)',
                  '&:hover': {
                    background: 'linear-gradient(45deg, #e55a2b 30%, #e5845c 90%)',
                  },
                }}
              >
                Join CS Arena Now
              </Button>
            </Box>
          </motion.div>
        </Container>
      </Box>
    </>
  );
};

export default HomePage;
