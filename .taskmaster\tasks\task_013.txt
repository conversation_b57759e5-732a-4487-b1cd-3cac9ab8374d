# Task ID: 13
# Title: Implement Team Management System
# Status: pending
# Dependencies: 3, 8
# Priority: medium
# Description: Develop the team creation, management, and invitation system.
# Details:
1. Create team data model
2. Implement team CRUD operations
3. Develop invitation system
4. Create team management interface
5. Implement team statistics tracking
6. Develop team profile pages

Features:
- Team creation with name, logo, description
- Member management with roles (owner, captain, member)
- Invitation system via email or username
- Team statistics aggregation
- Team profile customization

API Endpoints:
- POST /api/teams
- GET /api/teams/{teamId}
- PUT /api/teams/{teamId}
- DELETE /api/teams/{teamId}
- POST /api/teams/{teamId}/members
- DELETE /api/teams/{teamId}/members/{userId}
- POST /api/teams/{teamId}/invites

# Test Strategy:
Test team CRUD operations. Verify invitation flow. Test permission system for different roles. Ensure proper team statistics aggregation. Test with various team compositions.
