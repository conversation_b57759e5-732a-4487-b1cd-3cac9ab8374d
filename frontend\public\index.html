<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="CS 1.6 Arena - Competitive matchmaking platform for Counter-Strike 1.6"
    />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    
    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Rajdhani:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    
    <title>CS 1.6 Arena - Competitive Matchmaking</title>
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://csarena.com/">
    <meta property="og:title" content="CS 1.6 Arena - Competitive Matchmaking">
    <meta property="og:description" content="The ultimate competitive platform for Counter-Strike 1.6. Join thousands of players in ranked matches with professional-grade servers.">
    <meta property="og:image" content="%PUBLIC_URL%/og-image.jpg">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://csarena.com/">
    <meta property="twitter:title" content="CS 1.6 Arena - Competitive Matchmaking">
    <meta property="twitter:description" content="The ultimate competitive platform for Counter-Strike 1.6. Join thousands of players in ranked matches with professional-grade servers.">
    <meta property="twitter:image" content="%PUBLIC_URL%/og-image.jpg">
    
    <!-- Additional SEO -->
    <meta name="keywords" content="counter-strike, cs 1.6, matchmaking, esports, competitive gaming, fps, gaming platform">
    <meta name="author" content="CS Arena Team">
    <meta name="robots" content="index, follow">
    
    <!-- PWA iOS -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="CS Arena">
    
    <!-- Preload critical resources -->
    <link rel="preload" href="%PUBLIC_URL%/fonts/inter-var.woff2" as="font" type="font/woff2" crossorigin>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    
    <!-- Loading fallback -->
    <div id="loading-fallback" style="
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 9999;
      font-family: 'Inter', sans-serif;
      color: #fff;
    ">
      <div style="text-align: center;">
        <div style="
          width: 60px;
          height: 60px;
          border: 3px solid #333;
          border-top: 3px solid #ff6b35;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin: 0 auto 20px;
        "></div>
        <h2 style="margin: 0; font-weight: 600; font-size: 24px;">CS Arena</h2>
        <p style="margin: 10px 0 0; opacity: 0.7;">Loading the arena...</p>
      </div>
    </div>
    
    <style>
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      /* Hide loading fallback when React loads */
      #root:not(:empty) + #loading-fallback {
        display: none;
      }
    </style>
  </body>
</html>
