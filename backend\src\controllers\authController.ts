import { Request, Response } from 'express';
import { validationResult } from 'express-validator';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { v4 as uuidv4 } from 'uuid';

import { config } from '@/config/config';
import { DatabaseService } from '@/services/database';
import { RedisService } from '@/services/redis';
import { badRequest, unauthorized, notFound, conflict } from '@/middleware/errorHandler';
import { AuthenticatedRequest } from '@/middleware/auth';
import { logger } from '@/utils/logger';

export class AuthController {
  static async register(req: Request, res: Response): Promise<void> {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw badRequest('Validation failed');
    }

    const { email, username, password, displayName } = req.body;

    // Check if user already exists
    const existingUser = await DatabaseService.user.findFirst({
      where: {
        OR: [
          { email },
          { username },
        ],
      },
    });

    if (existingUser) {
      throw conflict('User with this email or username already exists');
    }

    // Hash password
    const passwordHash = await bcrypt.hash(password, 12);

    // Generate email verification token
    const emailVerificationToken = uuidv4();

    // Create user
    const user = await DatabaseService.user.create({
      data: {
        email,
        username,
        displayName: displayName || username,
        passwordHash,
        emailVerificationToken,
        profile: {
          create: {},
        },
        stats: {
          create: {},
        },
      },
      include: {
        profile: true,
        stats: true,
      },
    });

    // Generate JWT token
    const token = jwt.sign(
      { userId: user.id, email: user.email },
      config.jwtSecret,
      { expiresIn: config.jwtExpiresIn }
    );

    // Generate refresh token
    const refreshToken = jwt.sign(
      { userId: user.id, type: 'refresh' },
      config.jwtSecret,
      { expiresIn: config.jwtRefreshExpiresIn }
    );

    // Store refresh token in Redis
    await RedisService.setJSON(`refresh_token:${user.id}`, refreshToken, 30 * 24 * 60 * 60); // 30 days

    logger.info(`New user registered: ${user.username} (${user.email})`);

    res.status(201).json({
      success: true,
      data: {
        user: {
          id: user.id,
          email: user.email,
          username: user.username,
          displayName: user.displayName,
          avatar: user.avatar,
          role: user.role,
          emailVerified: user.emailVerified,
          createdAt: user.createdAt,
        },
        tokens: {
          accessToken: token,
          refreshToken,
          expiresIn: config.jwtExpiresIn,
        },
      },
    });
  }

  static async login(req: Request, res: Response): Promise<void> {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw badRequest('Validation failed');
    }

    const { email, password } = req.body;

    // Find user
    const user = await DatabaseService.user.findUnique({
      where: { email },
      include: {
        profile: true,
        stats: true,
      },
    });

    if (!user || !user.passwordHash) {
      throw unauthorized('Invalid credentials');
    }

    // Check password
    const isPasswordValid = await bcrypt.compare(password, user.passwordHash);
    if (!isPasswordValid) {
      throw unauthorized('Invalid credentials');
    }

    // Check if user is active
    if (!user.isActive) {
      throw unauthorized('Account is deactivated');
    }

    // Check if user is banned
    if (user.isBanned) {
      const banExpired = user.banExpires && new Date() > user.banExpires;
      if (!banExpired) {
        throw unauthorized(`Account is banned: ${user.banReason}`);
      }
      
      // Unban user if ban has expired
      await DatabaseService.user.update({
        where: { id: user.id },
        data: {
          isBanned: false,
          banReason: null,
          banExpires: null,
        },
      });
    }

    // Update last login
    await DatabaseService.user.update({
      where: { id: user.id },
      data: { lastLoginAt: new Date() },
    });

    // Generate JWT token
    const token = jwt.sign(
      { userId: user.id, email: user.email },
      config.jwtSecret,
      { expiresIn: config.jwtExpiresIn }
    );

    // Generate refresh token
    const refreshToken = jwt.sign(
      { userId: user.id, type: 'refresh' },
      config.jwtSecret,
      { expiresIn: config.jwtRefreshExpiresIn }
    );

    // Store refresh token in Redis
    await RedisService.setJSON(`refresh_token:${user.id}`, refreshToken, 30 * 24 * 60 * 60); // 30 days

    logger.info(`User logged in: ${user.username} (${user.email})`);

    res.json({
      success: true,
      data: {
        user: {
          id: user.id,
          email: user.email,
          username: user.username,
          displayName: user.displayName,
          avatar: user.avatar,
          role: user.role,
          emailVerified: user.emailVerified,
          premiumUntil: user.premiumUntil,
          stats: user.stats,
          createdAt: user.createdAt,
        },
        tokens: {
          accessToken: token,
          refreshToken,
          expiresIn: config.jwtExpiresIn,
        },
      },
    });
  }

  static async logout(req: AuthenticatedRequest, res: Response): Promise<void> {
    if (!req.user) {
      throw unauthorized('User not authenticated');
    }

    // Remove refresh token from Redis
    await RedisService.del(`refresh_token:${req.user.id}`);

    logger.info(`User logged out: ${req.user.username}`);

    res.json({
      success: true,
      message: 'Logged out successfully',
    });
  }

  static async refreshToken(req: Request, res: Response): Promise<void> {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      throw badRequest('Refresh token is required');
    }

    try {
      const decoded = jwt.verify(refreshToken, config.jwtSecret) as any;
      
      if (!decoded || decoded.type !== 'refresh') {
        throw unauthorized('Invalid refresh token');
      }

      // Check if refresh token exists in Redis
      const storedToken = await RedisService.getJSON(`refresh_token:${decoded.userId}`);
      if (!storedToken || storedToken !== refreshToken) {
        throw unauthorized('Invalid refresh token');
      }

      // Get user
      const user = await DatabaseService.user.findUnique({
        where: { id: decoded.userId },
        select: {
          id: true,
          email: true,
          username: true,
          isActive: true,
          isBanned: true,
        },
      });

      if (!user || !user.isActive || user.isBanned) {
        throw unauthorized('User not authorized');
      }

      // Generate new access token
      const newAccessToken = jwt.sign(
        { userId: user.id, email: user.email },
        config.jwtSecret,
        { expiresIn: config.jwtExpiresIn }
      );

      res.json({
        success: true,
        data: {
          accessToken: newAccessToken,
          expiresIn: config.jwtExpiresIn,
        },
      });
    } catch (error) {
      throw unauthorized('Invalid refresh token');
    }
  }

  static async forgotPassword(req: Request, res: Response): Promise<void> {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw badRequest('Validation failed');
    }

    const { email } = req.body;

    const user = await DatabaseService.user.findUnique({
      where: { email },
    });

    if (!user) {
      // Don't reveal if email exists
      res.json({
        success: true,
        message: 'If the email exists, a password reset link has been sent',
      });
      return;
    }

    // Generate reset token
    const resetToken = uuidv4();
    const resetExpires = new Date(Date.now() + 60 * 60 * 1000); // 1 hour

    await DatabaseService.user.update({
      where: { id: user.id },
      data: {
        passwordResetToken: resetToken,
        passwordResetExpires: resetExpires,
      },
    });

    // TODO: Send email with reset link
    logger.info(`Password reset requested for user: ${user.email}`);

    res.json({
      success: true,
      message: 'If the email exists, a password reset link has been sent',
    });
  }

  static async resetPassword(req: Request, res: Response): Promise<void> {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw badRequest('Validation failed');
    }

    const { token, password } = req.body;

    const user = await DatabaseService.user.findFirst({
      where: {
        passwordResetToken: token,
        passwordResetExpires: {
          gt: new Date(),
        },
      },
    });

    if (!user) {
      throw badRequest('Invalid or expired reset token');
    }

    // Hash new password
    const passwordHash = await bcrypt.hash(password, 12);

    await DatabaseService.user.update({
      where: { id: user.id },
      data: {
        passwordHash,
        passwordResetToken: null,
        passwordResetExpires: null,
      },
    });

    logger.info(`Password reset completed for user: ${user.email}`);

    res.json({
      success: true,
      message: 'Password reset successfully',
    });
  }

  static async changePassword(req: AuthenticatedRequest, res: Response): Promise<void> {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw badRequest('Validation failed');
    }

    if (!req.user) {
      throw unauthorized('User not authenticated');
    }

    const { currentPassword, newPassword } = req.body;

    const user = await DatabaseService.user.findUnique({
      where: { id: req.user.id },
    });

    if (!user || !user.passwordHash) {
      throw notFound('User not found');
    }

    // Verify current password
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.passwordHash);
    if (!isCurrentPasswordValid) {
      throw badRequest('Current password is incorrect');
    }

    // Hash new password
    const passwordHash = await bcrypt.hash(newPassword, 12);

    await DatabaseService.user.update({
      where: { id: user.id },
      data: { passwordHash },
    });

    logger.info(`Password changed for user: ${user.email}`);

    res.json({
      success: true,
      message: 'Password changed successfully',
    });
  }

  static async verifyEmail(req: Request, res: Response): Promise<void> {
    const { token } = req.body;

    if (!token) {
      throw badRequest('Verification token is required');
    }

    const user = await DatabaseService.user.findFirst({
      where: { emailVerificationToken: token },
    });

    if (!user) {
      throw badRequest('Invalid verification token');
    }

    await DatabaseService.user.update({
      where: { id: user.id },
      data: {
        emailVerified: true,
        emailVerificationToken: null,
      },
    });

    logger.info(`Email verified for user: ${user.email}`);

    res.json({
      success: true,
      message: 'Email verified successfully',
    });
  }

  static async resendVerification(req: AuthenticatedRequest, res: Response): Promise<void> {
    if (!req.user) {
      throw unauthorized('User not authenticated');
    }

    const user = await DatabaseService.user.findUnique({
      where: { id: req.user.id },
    });

    if (!user) {
      throw notFound('User not found');
    }

    if (user.emailVerified) {
      throw badRequest('Email is already verified');
    }

    // Generate new verification token
    const emailVerificationToken = uuidv4();

    await DatabaseService.user.update({
      where: { id: user.id },
      data: { emailVerificationToken },
    });

    // TODO: Send verification email

    res.json({
      success: true,
      message: 'Verification email sent',
    });
  }

  static async getProfile(req: AuthenticatedRequest, res: Response): Promise<void> {
    if (!req.user) {
      throw unauthorized('User not authenticated');
    }

    const user = await DatabaseService.user.findUnique({
      where: { id: req.user.id },
      include: {
        profile: true,
        stats: true,
      },
    });

    if (!user) {
      throw notFound('User not found');
    }

    res.json({
      success: true,
      data: {
        id: user.id,
        email: user.email,
        username: user.username,
        displayName: user.displayName,
        avatar: user.avatar,
        role: user.role,
        emailVerified: user.emailVerified,
        premiumUntil: user.premiumUntil,
        profile: user.profile,
        stats: user.stats,
        createdAt: user.createdAt,
      },
    });
  }

  static async updateProfile(req: AuthenticatedRequest, res: Response): Promise<void> {
    if (!req.user) {
      throw unauthorized('User not authenticated');
    }

    const { displayName, ...profileData } = req.body;

    // Update user
    const updatedUser = await DatabaseService.user.update({
      where: { id: req.user.id },
      data: {
        displayName,
        profile: {
          update: profileData,
        },
      },
      include: {
        profile: true,
      },
    });

    res.json({
      success: true,
      data: updatedUser,
    });
  }

  // OAuth methods (placeholder implementations)
  static async googleAuth(req: Request, res: Response): Promise<void> {
    // TODO: Implement Google OAuth
    res.json({ success: false, message: 'Google OAuth not implemented yet' });
  }

  static async googleCallback(req: Request, res: Response): Promise<void> {
    // TODO: Implement Google OAuth callback
    res.json({ success: false, message: 'Google OAuth callback not implemented yet' });
  }

  static async steamAuth(req: Request, res: Response): Promise<void> {
    // TODO: Implement Steam OAuth
    res.json({ success: false, message: 'Steam OAuth not implemented yet' });
  }

  static async steamCallback(req: Request, res: Response): Promise<void> {
    // TODO: Implement Steam OAuth callback
    res.json({ success: false, message: 'Steam OAuth callback not implemented yet' });
  }
}
