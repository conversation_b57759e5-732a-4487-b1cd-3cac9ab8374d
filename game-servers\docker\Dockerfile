# CS 1.6 Server Dockerfile
FROM ubuntu:20.04

# Avoid interactive prompts during package installation
ENV DEBIAN_FRONTEND=noninteractive

# Install dependencies
RUN apt-get update && apt-get install -y \
    lib32gcc1 \
    lib32stdc++6 \
    wget \
    unzip \
    curl \
    net-tools \
    && rm -rf /var/lib/apt/lists/*

# Create steam user
RUN useradd -m steam

# Set working directory
WORKDIR /home/<USER>

# Download and install SteamCMD
RUN wget https://steamcdn-a.akamaihd.net/client/installer/steamcmd_linux.tar.gz \
    && tar -xvzf steamcmd_linux.tar.gz \
    && rm steamcmd_linux.tar.gz

# Create directories
RUN mkdir -p /home/<USER>/hlds \
    && mkdir -p /home/<USER>/configs \
    && mkdir -p /home/<USER>/logs

# Download and install Half-Life Dedicated Server
RUN ./steamcmd.sh +login anonymous +force_install_dir /home/<USER>/hlds +app_update 90 validate +quit

# Download Counter-Strike 1.6
RUN ./steamcmd.sh +login anonymous +force_install_dir /home/<USER>/hlds +app_update 70 validate +quit

# Copy server configuration files
COPY configs/ /home/<USER>/configs/
COPY scripts/ /home/<USER>/scripts/

# Make scripts executable
RUN chmod +x /home/<USER>/scripts/*.sh

# Change ownership to steam user
RUN chown -R steam:steam /home/<USER>

# Switch to steam user
USER steam

# Expose ports
EXPOSE 27015/udp
EXPOSE 27015/tcp

# Environment variables with defaults
ENV SERVER_NAME="CS Arena Server"
ENV MAX_PLAYERS=10
ENV MAP=de_dust2
ENV RCON_PASSWORD=cs_arena_rcon
ENV SERVER_PASSWORD=""
ENV TICKRATE=100

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD netstat -an | grep :27015 > /dev/null; if [ 0 != $? ]; then exit 1; fi;

# Start the server
CMD ["/home/<USER>/scripts/start-server.sh"]
