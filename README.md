# CS 1.6 Arena

A competitive matchmaking platform for Counter-Strike 1.6, providing professional-grade gaming experience with automated server provisioning and intelligent matchmaking.

## 🎯 Project Overview

CS 1.6 Arena is a modern platform that brings the classic Counter-Strike 1.6 experience to today's competitive gaming standards. Similar to GamersClub and FACEIT, but specifically designed for CS 1.6 enthusiasts.

### Key Features

- **Intelligent Matchmaking**: ELO-based system with skill rating, ping, and preference matching
- **Auto Server Provisioning**: Dynamic Docker container creation for game servers
- **Ranking System**: Bronze to Elite divisions with detailed statistics
- **Tournament Support**: Automated bracket generation and prize pool management
- **Anti-Cheat Integration**: Modern cheat detection systems
- **Mobile Support**: React Native app for iOS and Android

## 🏗️ Architecture

### Backend Stack
- **Framework**: Node.js with Express.js
- **Database**: PostgreSQL (primary) + Redis (cache/sessions)
- **Message Queue**: RabbitMQ for async processing
- **Authentication**: JWT + OAuth2 for social login

### Frontend Stack
- **Web App**: React.js with TypeScript
- **Mobile App**: React Native
- **State Management**: Redux Toolkit
- **UI Framework**: Material-UI (customized)

### Infrastructure
- **Cloud Provider**: AWS (EC2, RDS, ElastiCache)
- **Containerization**: Docker + Kubernetes
- **CDN**: CloudFlare for static assets
- **Monitoring**: DataDog for observability

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ and npm
- Docker and Docker Compose
- PostgreSQL 14+
- Redis 6+

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-org/cs-arena.git
   cd cs-arena
   ```

2. **Install dependencies**
   ```bash
   npm run setup
   ```

3. **Setup environment variables**
   ```bash
   cp backend/.env.example backend/.env
   cp frontend/.env.example frontend/.env
   # Edit the .env files with your configuration
   ```

4. **Start development environment**
   ```bash
   # Using Docker (recommended)
   npm run docker:up
   
   # Or run locally
   npm run dev
   ```

5. **Access the application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:5000
   - Admin Dashboard: http://localhost:3000/admin

## 📁 Project Structure

```
cs-arena/
├── backend/                 # Node.js/Express API server
│   ├── src/
│   │   ├── controllers/     # Route controllers
│   │   ├── models/          # Database models
│   │   ├── services/        # Business logic
│   │   ├── middleware/      # Express middleware
│   │   ├── routes/          # API routes
│   │   └── utils/           # Utility functions
│   ├── tests/               # Backend tests
│   └── package.json
├── frontend/                # React.js web application
│   ├── src/
│   │   ├── components/      # React components
│   │   ├── pages/           # Page components
│   │   ├── store/           # Redux store
│   │   ├── services/        # API services
│   │   └── utils/           # Utility functions
│   ├── public/              # Static assets
│   └── package.json
├── mobile/                  # React Native mobile app
├── infrastructure/          # Docker, K8s, and deployment configs
│   ├── docker/              # Docker configurations
│   ├── kubernetes/          # K8s manifests
│   └── terraform/           # Infrastructure as code
├── game-servers/            # CS 1.6 server configurations
│   ├── docker/              # CS 1.6 Docker setup
│   └── configs/             # Server configurations
└── docs/                    # Documentation
```

## 🎮 Game Server Management

The platform automatically provisions CS 1.6 servers using Docker containers:

- **Dynamic Provisioning**: Servers created on-demand for matches
- **Auto-Scaling**: Based on player demand and server load
- **Custom Configurations**: Map rotation, game rules, anti-cheat
- **Health Monitoring**: Automatic cleanup of finished matches

## 🔧 Development

### Running Tests
```bash
npm test                    # Run all tests
npm run test:backend       # Backend tests only
npm run test:frontend      # Frontend tests only
```

### Code Quality
```bash
npm run lint               # Lint all code
npm run lint:fix           # Fix linting issues
```

### Database Management
```bash
cd backend
npm run db:migrate         # Run migrations
npm run db:seed            # Seed test data
npm run db:reset           # Reset database
```

## 🚢 Deployment

### Production Build
```bash
npm run build              # Build all applications
npm run docker:build       # Build Docker images
```

### Kubernetes Deployment
```bash
kubectl apply -f infrastructure/kubernetes/
```

## 📊 Monitoring & Analytics

- **Application Metrics**: Response times, error rates, throughput
- **Infrastructure Metrics**: CPU, memory, network usage
- **Business Metrics**: MAU, retention rate, revenue per user
- **Game Metrics**: Match duration, player performance, server health

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines

- Follow TypeScript best practices
- Write tests for new features
- Use conventional commit messages
- Update documentation for API changes

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🎯 Roadmap

### Phase 1 - MVP (6 months)
- [x] Basic matchmaking system
- [x] Server auto-provisioning
- [x] Web interface
- [x] Simple ranking system

### Phase 2 - Enhanced Features (3 months)
- [ ] Mobile application
- [ ] Team management
- [ ] Basic tournaments
- [ ] Advanced statistics

### Phase 3 - Platform Growth (3 months)
- [ ] Premium monetization
- [ ] Replay system
- [ ] Social features
- [ ] LATAM expansion

## 🆘 Support

- **Documentation**: [docs.csarena.com](https://docs.csarena.com)
- **Discord**: [CS Arena Community](https://discord.gg/csarena)
- **Email**: <EMAIL>
- **Issues**: [GitHub Issues](https://github.com/your-org/cs-arena/issues)

---

**Made with ❤️ by the CS Arena Team**
