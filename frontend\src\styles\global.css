/* Global styles for CS Arena */

* {
  box-sizing: border-box;
}

html {
  height: 100%;
  scroll-behavior: smooth;
}

body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: 'Inter', 'Roboto', 'Helvetica', 'Arial', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
  color: #ffffff;
  overflow-x: hidden;
}

#root {
  height: 100%;
  min-height: 100vh;
}

/* Custom scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #2b2b2b;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #6b6b6b;
  border-radius: 4px;
  border: 2px solid #2b2b2b;
}

::-webkit-scrollbar-thumb:hover {
  background: #959595;
}

::-webkit-scrollbar-corner {
  background: #2b2b2b;
}

/* Firefox scrollbar */
* {
  scrollbar-width: thin;
  scrollbar-color: #6b6b6b #2b2b2b;
}

/* Selection styles */
::selection {
  background-color: rgba(255, 107, 53, 0.3);
  color: #ffffff;
}

::-moz-selection {
  background-color: rgba(255, 107, 53, 0.3);
  color: #ffffff;
}

/* Focus styles */
*:focus {
  outline: 2px solid #ff6b35;
  outline-offset: 2px;
}

/* Remove focus outline for mouse users */
.js-focus-visible *:focus:not(.focus-visible) {
  outline: none;
}

/* Loading animations */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInDown {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Utility classes */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.slide-in-up {
  animation: slideInUp 0.3s ease-out;
}

.slide-in-down {
  animation: slideInDown 0.3s ease-out;
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

.pulse {
  animation: pulse 2s infinite;
}

/* Text utilities */
.text-gradient {
  background: linear-gradient(45deg, #ff6b35 30%, #ff9566 90%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-shadow {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

/* Gaming-specific styles */
.rank-bronze { color: #cd7f32; }
.rank-silver { color: #c0c0c0; }
.rank-gold { color: #ffd700; }
.rank-diamond { color: #b9f2ff; }
.rank-elite { color: #ff1493; }

.stat-kill { color: #4caf50; }
.stat-death { color: #f44336; }
.stat-assist { color: #2196f3; }
.stat-headshot { color: #ff9800; }
.stat-mvp { color: #ffd700; }

/* Card hover effects */
.card-hover {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.card-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
}

/* Button effects */
.btn-glow {
  position: relative;
  overflow: hidden;
}

.btn-glow::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-glow:hover::before {
  left: 100%;
}

/* Map background patterns */
.map-dust2 {
  background: linear-gradient(135deg, #d4a574 0%, #8b6914 100%);
}

.map-inferno {
  background: linear-gradient(135deg, #ff4500 0%, #8b0000 100%);
}

.map-nuke {
  background: linear-gradient(135deg, #708090 0%, #2f4f4f 100%);
}

.map-train {
  background: linear-gradient(135deg, #4682b4 0%, #191970 100%);
}

.map-cache {
  background: linear-gradient(135deg, #daa520 0%, #b8860b 100%);
}

/* Responsive utilities */
@media (max-width: 600px) {
  .hide-mobile {
    display: none !important;
  }
}

@media (min-width: 601px) {
  .show-mobile {
    display: none !important;
  }
}

/* Print styles */
@media print {
  body {
    background: white !important;
    color: black !important;
  }
  
  .no-print {
    display: none !important;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .card-hover {
    border: 2px solid #ffffff;
  }
}

/* Dark mode preference (already dark by default) */
@media (prefers-color-scheme: light) {
  /* Keep dark theme even if user prefers light */
}
