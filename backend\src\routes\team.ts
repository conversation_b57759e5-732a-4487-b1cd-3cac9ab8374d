import { Router } from 'express';
import { body, param, query } from 'express-validator';
import { TeamController } from '@/controllers/teamController';
import { asyncHandler } from '@/middleware/errorHandler';

const router = Router();

// Validation rules
const createTeamValidation = [
  body('name').isLength({ min: 3, max: 50 }),
  body('tag').isLength({ min: 2, max: 8 }).matches(/^[A-Z0-9]+$/),
  body('description').optional().isLength({ max: 500 }),
];

const teamIdValidation = [
  param('teamId').isUUID(),
];

const updateTeamValidation = [
  body('name').optional().isLength({ min: 3, max: 50 }),
  body('tag').optional().isLength({ min: 2, max: 8 }).matches(/^[A-Z0-9]+$/),
  body('description').optional().isLength({ max: 500 }),
];

const inviteValidation = [
  body('userId').isUUID(),
  body('role').optional().isIn(['MEMBER', 'CAPTAIN']),
];

const searchValidation = [
  query('q').optional().isLength({ min: 2, max: 50 }),
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 50 }),
];

// Routes
router.get('/', searchValidation, asyncHandler(TeamController.getTeams));
router.post('/', createTeamValidation, asyncHandler(TeamController.createTeam));

router.get('/my-teams', asyncHandler(TeamController.getMyTeams));
router.get('/invitations', asyncHandler(TeamController.getTeamInvitations));

router.get('/:teamId', teamIdValidation, asyncHandler(TeamController.getTeam));
router.put('/:teamId', teamIdValidation, updateTeamValidation, asyncHandler(TeamController.updateTeam));
router.delete('/:teamId', teamIdValidation, asyncHandler(TeamController.deleteTeam));

// Team membership
router.get('/:teamId/members', teamIdValidation, asyncHandler(TeamController.getTeamMembers));
router.post('/:teamId/invite', teamIdValidation, inviteValidation, asyncHandler(TeamController.inviteToTeam));
router.post('/:teamId/join', teamIdValidation, asyncHandler(TeamController.joinTeam));
router.delete('/:teamId/leave', teamIdValidation, asyncHandler(TeamController.leaveTeam));
router.delete('/:teamId/members/:userId', teamIdValidation, asyncHandler(TeamController.removeMember));

// Team management
router.put('/:teamId/members/:userId/role', teamIdValidation, asyncHandler(TeamController.updateMemberRole));
router.post('/:teamId/transfer-ownership', teamIdValidation, asyncHandler(TeamController.transferOwnership));

// Team logo
router.post('/:teamId/logo', teamIdValidation, asyncHandler(TeamController.uploadLogo));
router.delete('/:teamId/logo', teamIdValidation, asyncHandler(TeamController.deleteLogo));

// Team stats
router.get('/:teamId/stats', teamIdValidation, asyncHandler(TeamController.getTeamStats));
router.get('/:teamId/matches', teamIdValidation, asyncHandler(TeamController.getTeamMatches));

export default router;
