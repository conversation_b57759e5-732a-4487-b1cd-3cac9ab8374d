import { Server, Socket } from 'socket.io';
import jwt from 'jsonwebtoken';
import { config } from '@/config/config';
import { logger } from '@/utils/logger';
import { DatabaseService } from '@/services/database';
import { RedisService } from '@/services/redis';

interface AuthenticatedSocket extends Socket {
  userId?: string;
  username?: string;
}

class SocketServiceClass {
  private static instance: SocketServiceClass;
  private io: Server | null = null;
  private connectedUsers: Map<string, string> = new Map(); // userId -> socketId

  private constructor() {}

  public static getInstance(): SocketServiceClass {
    if (!SocketServiceClass.instance) {
      SocketServiceClass.instance = new SocketServiceClass();
    }
    return SocketServiceClass.instance;
  }

  public initialize(io: Server): void {
    this.io = io;
    this.setupMiddleware();
    this.setupEventHandlers();
    logger.info('Socket.IO service initialized');
  }

  private setupMiddleware(): void {
    if (!this.io) return;

    // Authentication middleware
    this.io.use(async (socket: AuthenticatedSocket, next) => {
      try {
        const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', '');
        
        if (!token) {
          return next(new Error('Authentication token required'));
        }

        const decoded = jwt.verify(token, config.jwtSecret) as any;
        
        if (!decoded || !decoded.userId) {
          return next(new Error('Invalid token'));
        }

        // Get user from database
        const user = await DatabaseService.user.findUnique({
          where: { id: decoded.userId },
          select: {
            id: true,
            username: true,
            isActive: true,
            isBanned: true,
          },
        });

        if (!user || !user.isActive || user.isBanned) {
          return next(new Error('User not authorized'));
        }

        socket.userId = user.id;
        socket.username = user.username;
        next();
      } catch (error) {
        next(new Error('Authentication failed'));
      }
    });
  }

  private setupEventHandlers(): void {
    if (!this.io) return;

    this.io.on('connection', (socket: AuthenticatedSocket) => {
      this.handleConnection(socket);
    });
  }

  private async handleConnection(socket: AuthenticatedSocket): Promise<void> {
    if (!socket.userId) return;

    logger.info(`User ${socket.username} connected with socket ${socket.id}`);

    // Store user connection
    this.connectedUsers.set(socket.userId, socket.id);
    await RedisService.setJSON(`socket:${socket.userId}`, {
      socketId: socket.id,
      connectedAt: new Date().toISOString(),
    }, 3600);

    // Join user to their personal room
    socket.join(`user:${socket.userId}`);

    // Update user's online status
    await this.updateUserOnlineStatus(socket.userId, true);

    // Setup event handlers
    this.setupUserEventHandlers(socket);

    // Handle disconnection
    socket.on('disconnect', () => {
      this.handleDisconnection(socket);
    });
  }

  private async handleDisconnection(socket: AuthenticatedSocket): Promise<void> {
    if (!socket.userId) return;

    logger.info(`User ${socket.username} disconnected`);

    // Remove user connection
    this.connectedUsers.delete(socket.userId);
    await RedisService.del(`socket:${socket.userId}`);

    // Update user's online status
    await this.updateUserOnlineStatus(socket.userId, false);
  }

  private setupUserEventHandlers(socket: AuthenticatedSocket): void {
    // Matchmaking events
    socket.on('matchmaking:join', (data) => {
      this.handleMatchmakingJoin(socket, data);
    });

    socket.on('matchmaking:leave', () => {
      this.handleMatchmakingLeave(socket);
    });

    socket.on('match:accept', (data) => {
      this.handleMatchAccept(socket, data);
    });

    socket.on('match:decline', (data) => {
      this.handleMatchDecline(socket, data);
    });

    // Chat events
    socket.on('chat:join', (data) => {
      this.handleChatJoin(socket, data);
    });

    socket.on('chat:leave', (data) => {
      this.handleChatLeave(socket, data);
    });

    socket.on('chat:message', (data) => {
      this.handleChatMessage(socket, data);
    });

    // Team events
    socket.on('team:join', (data) => {
      this.handleTeamJoin(socket, data);
    });

    socket.on('team:leave', (data) => {
      this.handleTeamLeave(socket, data);
    });

    // Tournament events
    socket.on('tournament:join', (data) => {
      this.handleTournamentJoin(socket, data);
    });

    // General events
    socket.on('ping', () => {
      socket.emit('pong', { timestamp: Date.now() });
    });
  }

  // Event handlers
  private async handleMatchmakingJoin(socket: AuthenticatedSocket, data: any): Promise<void> {
    if (!socket.userId) return;

    socket.join('matchmaking');
    logger.info(`User ${socket.username} joined matchmaking queue`);

    // Emit to matchmaking service
    socket.to('matchmaking').emit('user:joined', {
      userId: socket.userId,
      username: socket.username,
      preferences: data,
    });
  }

  private async handleMatchmakingLeave(socket: AuthenticatedSocket): Promise<void> {
    if (!socket.userId) return;

    socket.leave('matchmaking');
    logger.info(`User ${socket.username} left matchmaking queue`);

    socket.to('matchmaking').emit('user:left', {
      userId: socket.userId,
      username: socket.username,
    });
  }

  private async handleMatchAccept(socket: AuthenticatedSocket, data: any): Promise<void> {
    if (!socket.userId) return;

    logger.info(`User ${socket.username} accepted match ${data.matchId}`);
    
    // Emit to match room
    this.io?.to(`match:${data.matchId}`).emit('match:player_accepted', {
      userId: socket.userId,
      username: socket.username,
    });
  }

  private async handleMatchDecline(socket: AuthenticatedSocket, data: any): Promise<void> {
    if (!socket.userId) return;

    logger.info(`User ${socket.username} declined match ${data.matchId}`);
    
    // Emit to match room
    this.io?.to(`match:${data.matchId}`).emit('match:player_declined', {
      userId: socket.userId,
      username: socket.username,
    });
  }

  private async handleChatJoin(socket: AuthenticatedSocket, data: { channel: string }): Promise<void> {
    socket.join(`chat:${data.channel}`);
    logger.debug(`User ${socket.username} joined chat channel ${data.channel}`);
  }

  private async handleChatLeave(socket: AuthenticatedSocket, data: { channel: string }): Promise<void> {
    socket.leave(`chat:${data.channel}`);
    logger.debug(`User ${socket.username} left chat channel ${data.channel}`);
  }

  private async handleChatMessage(socket: AuthenticatedSocket, data: any): Promise<void> {
    if (!socket.userId) return;

    // Save message to database
    const message = await DatabaseService.chatMessage.create({
      data: {
        senderId: socket.userId,
        type: data.type || 'GLOBAL',
        channel: data.channel,
        content: data.content,
      },
      include: {
        sender: {
          select: {
            username: true,
            avatar: true,
          },
        },
      },
    });

    // Emit to channel
    this.io?.to(`chat:${data.channel}`).emit('chat:message', {
      id: message.id,
      content: message.content,
      sender: message.sender,
      timestamp: message.createdAt,
    });
  }

  private async handleTeamJoin(socket: AuthenticatedSocket, data: { teamId: string }): Promise<void> {
    socket.join(`team:${data.teamId}`);
    logger.debug(`User ${socket.username} joined team room ${data.teamId}`);
  }

  private async handleTeamLeave(socket: AuthenticatedSocket, data: { teamId: string }): Promise<void> {
    socket.leave(`team:${data.teamId}`);
    logger.debug(`User ${socket.username} left team room ${data.teamId}`);
  }

  private async handleTournamentJoin(socket: AuthenticatedSocket, data: { tournamentId: string }): Promise<void> {
    socket.join(`tournament:${data.tournamentId}`);
    logger.debug(`User ${socket.username} joined tournament room ${data.tournamentId}`);
  }

  // Public methods for emitting events
  public async emitToUser(userId: string, event: string, data: any): Promise<boolean> {
    if (!this.io) return false;

    const socketId = this.connectedUsers.get(userId);
    if (socketId) {
      this.io.to(socketId).emit(event, data);
      return true;
    }

    return false;
  }

  public async emitToRoom(room: string, event: string, data: any): Promise<void> {
    if (!this.io) return;
    this.io.to(room).emit(event, data);
  }

  public async emitToAll(event: string, data: any): Promise<void> {
    if (!this.io) return;
    this.io.emit(event, data);
  }

  // Matchmaking specific methods
  public async notifyMatchFound(players: string[], matchData: any): Promise<void> {
    for (const playerId of players) {
      await this.emitToUser(playerId, 'match:found', matchData);
    }
  }

  public async notifyMatchStarted(players: string[], matchData: any): Promise<void> {
    for (const playerId of players) {
      await this.emitToUser(playerId, 'match:started', matchData);
    }
  }

  public async notifyMatchEnded(players: string[], matchData: any): Promise<void> {
    for (const playerId of players) {
      await this.emitToUser(playerId, 'match:ended', matchData);
    }
  }

  // Utility methods
  private async updateUserOnlineStatus(userId: string, isOnline: boolean): Promise<void> {
    await DatabaseService.user.update({
      where: { id: userId },
      data: {
        lastLoginAt: isOnline ? new Date() : undefined,
      },
    });

    // Update Redis cache
    await RedisService.setJSON(`user:${userId}:online`, isOnline, 300);
  }

  public async isUserOnline(userId: string): Promise<boolean> {
    return this.connectedUsers.has(userId);
  }

  public getConnectedUsersCount(): number {
    return this.connectedUsers.size;
  }
}

// Export singleton instance
export const SocketService = SocketServiceClass.getInstance();
